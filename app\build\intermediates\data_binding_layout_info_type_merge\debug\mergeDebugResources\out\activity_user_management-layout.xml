<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_management" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_user_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_user_management_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="51"/></Target><Target id="@+id/cardSearch" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="10" startOffset="4" endLine="49" endOffset="39"/></Target><Target id="@+id/tilSearch" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="25" startOffset="12" endLine="40" endOffset="67"/></Target><Target id="@+id/etSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="33" startOffset="16" endLine="39" endOffset="42"/></Target><Target id="@+id/btnSearch" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="42" startOffset="12" endLine="47" endOffset="35"/></Target><Target id="@+id/swipeRefresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="52" startOffset="4" endLine="67" endOffset="59"/></Target><Target id="@+id/rvUsers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="60" startOffset="8" endLine="66" endOffset="48"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="70" startOffset="4" endLine="78" endOffset="51"/></Target><Target id="@+id/tvEmpty" view="TextView"><Expressions/><location startLine="81" startOffset="4" endLine="91" endOffset="51"/></Target></Targets></Layout>