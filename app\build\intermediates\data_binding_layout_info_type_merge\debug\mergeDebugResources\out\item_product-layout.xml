<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product" modulePackage="com.opms" filePath="app\src\main\res\layout\item_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_product"><Targets><Target id="@+id/card_product" tag="layout/item_product_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="51"/></Target><Target id="@+id/iv_product_image" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="26" endOffset="59"/></Target><Target id="@+id/tv_product_name" view="TextView"><Expressions/><location startLine="42" startOffset="16" endLine="50" endOffset="46"/></Target><Target id="@+id/tv_product_status" view="TextView"><Expressions/><location startLine="52" startOffset="16" endLine="62" endOffset="45"/></Target><Target id="@+id/tv_product_code" view="TextView"><Expressions/><location startLine="67" startOffset="12" endLine="74" endOffset="41"/></Target><Target id="@+id/tv_product_model" view="TextView"><Expressions/><location startLine="77" startOffset="12" endLine="84" endOffset="41"/></Target><Target id="@+id/tv_product_standard" view="TextView"><Expressions/><location startLine="87" startOffset="12" endLine="94" endOffset="41"/></Target><Target id="@+id/tv_product_price" view="TextView"><Expressions/><location startLine="104" startOffset="16" endLine="112" endOffset="46"/></Target><Target id="@+id/btn_edit" view="ImageView"><Expressions/><location startLine="115" startOffset="16" endLine="125" endOffset="52"/></Target><Target id="@+id/btn_delete" view="ImageView"><Expressions/><location startLine="128" startOffset="16" endLine="137" endOffset="50"/></Target></Targets></Layout>