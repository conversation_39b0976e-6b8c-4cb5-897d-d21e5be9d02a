<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_permission" modulePackage="com.opms" filePath="app\src\main\res\layout\item_permission.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_permission_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="249" endOffset="51"/></Target><Target id="@+id/iv_permission_icon" view="ImageView"><Expressions/><location startLine="20" startOffset="8" endLine="29" endOffset="39"/></Target><Target id="@+id/ll_view_mode" view="LinearLayout"><Expressions/><location startLine="40" startOffset="12" endLine="133" endOffset="26"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="53" startOffset="20" endLine="61" endOffset="43"/></Target><Target id="@+id/tv_type" view="TextView"><Expressions/><location startLine="63" startOffset="20" endLine="74" endOffset="41"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="76" startOffset="20" endLine="86" endOffset="58"/></Target><Target id="@+id/tv_code" view="TextView"><Expressions/><location startLine="97" startOffset="20" endLine="104" endOffset="50"/></Target><Target id="@+id/tv_path" view="TextView"><Expressions/><location startLine="106" startOffset="20" endLine="116" endOffset="51"/></Target><Target id="@+id/tv_description" view="TextView"><Expressions/><location startLine="121" startOffset="16" endLine="130" endOffset="57"/></Target><Target id="@+id/ll_edit_mode" view="LinearLayout"><Expressions/><location startLine="136" startOffset="12" endLine="229" endOffset="26"/></Target><Target id="@+id/til_edit_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="144" startOffset="16" endLine="159" endOffset="71"/></Target><Target id="@+id/et_edit_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="152" startOffset="20" endLine="157" endOffset="48"/></Target><Target id="@+id/til_edit_description" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="162" startOffset="16" endLine="178" endOffset="71"/></Target><Target id="@+id/et_edit_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="170" startOffset="20" endLine="176" endOffset="46"/></Target><Target id="@+id/switch_edit_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="194" startOffset="20" endLine="199" endOffset="57"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="207" startOffset="20" endLine="215" endOffset="49"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="218" startOffset="20" endLine="225" endOffset="49"/></Target><Target id="@+id/iv_delete" view="ImageView"><Expressions/><location startLine="234" startOffset="8" endLine="245" endOffset="37"/></Target></Targets></Layout>