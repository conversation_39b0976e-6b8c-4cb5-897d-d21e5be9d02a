1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.opms"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 存储权限 -->
16    <uses-permission
16-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:10:5-12:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:11:9-64
18        android:maxSdkVersion="32" />
18-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:12:9-35
19    <uses-permission
19-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:13:5-15:38
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:14:9-65
21        android:maxSdkVersion="32" />
21-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:15:9-35
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:16:5-76
22-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:16:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:17:5-75
23-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:17:22-72
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:18:5-75
24-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:18:22-72
25
26    <!-- 相机权限 -->
27    <uses-permission android:name="android.permission.CAMERA" />
27-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:21:5-65
27-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:21:22-62
28
29    <uses-feature
29-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:22:5-24:36
30        android:name="android.hardware.camera"
30-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:23:9-47
31        android:required="false" />
31-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:24:9-33
32
33    <permission
33-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.opms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.opms.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:26:5-267:19
40        android:name="com.opms.OPMSApplication"
40-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:27:9-40
41        android:allowBackup="true"
41-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:28:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.12.0] D:\Android\GradleRepository\caches\8.11.1\transforms\f5d6d0c532ad067d6a1644a9775866cb\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:29:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:30:9-54
47        android:icon="@mipmap/ic_launcher"
47-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:31:9-43
48        android:label="@string/app_name"
48-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:32:9-41
49        android:requestLegacyExternalStorage="true"
49-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:37:9-52
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:33:9-54
51        android:supportsRtl="true"
51-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:34:9-35
52        android:theme="@style/Theme.OPMS"
52-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:35:9-42
53        android:usesCleartextTraffic="true" >
53-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:36:9-44
54
55        <!-- 启动页 -->
56        <activity
56-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:41:9-49:20
57            android:name="com.opms.ui.splash.SplashActivity"
57-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:42:13-53
58            android:exported="true"
58-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:43:13-36
59            android:theme="@style/Theme.OPMS.NoActionBar" >
59-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:44:13-58
60            <intent-filter>
60-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:45:13-48:29
61                <action android:name="android.intent.action.MAIN" />
61-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:46:17-69
61-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:46:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:47:17-77
63-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:47:27-74
64            </intent-filter>
65        </activity>
66
67        <!-- 登录页 -->
68        <activity
68-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:52:9-55:61
69            android:name="com.opms.ui.login.LoginActivity"
69-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:53:13-51
70            android:exported="false"
70-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:54:13-37
71            android:theme="@style/Theme.OPMS.NoActionBar" />
71-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:55:13-58
72
73        <!-- 注册页 -->
74        <activity
74-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:58:9-61:61
75            android:name="com.opms.ui.register.RegisterActivity"
75-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:59:13-57
76            android:exported="false"
76-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:60:13-37
77            android:theme="@style/Theme.OPMS.NoActionBar" />
77-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:61:13-58
78
79        <!-- 主页面 -->
80        <activity
80-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:64:9-67:61
81            android:name="com.opms.MainActivity"
81-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:65:13-41
82            android:exported="false"
82-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:66:13-37
83            android:theme="@style/Theme.OPMS.NoActionBar" />
83-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:67:13-58
84
85        <!-- 文件提供者 -->
86        <provider
87            android:name="androidx.core.content.FileProvider"
87-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:71:13-62
88            android:authorities="com.opms.fileprovider"
88-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:72:13-64
89            android:exported="false"
89-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:73:13-37
90            android:grantUriPermissions="true" >
90-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:74:13-47
91            <meta-data
91-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:75:13-77:54
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:76:17-67
93                android:resource="@xml/file_paths" />
93-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:77:17-51
94        </provider>
95
96        <!--
97        这些功能尚未实现，暂时注释掉
98        <activity
99            android:name=".ui.UserAuditActivity"
100            android:exported="false"
101            android:label="用户审核" />
102
103        <activity
104            android:name=".ui.RegistrationDetailActivity"
105            android:exported="false"
106            android:label="注册申请详情" />
107        -->
108
109
110        <!-- 用户资料页面 -->
111        <activity
111-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:93:9-95:40
112            android:name="com.opms.ui.profile.ProfileActivity"
112-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:94:13-55
113            android:exported="false" />
113-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:95:13-37
114
115        <!-- 头像查看/编辑页面 -->
116        <activity
116-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:98:9-101:61
117            android:name="com.opms.ui.profile.AvatarViewActivity"
117-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:99:13-58
118            android:exported="false"
118-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:100:13-37
119            android:theme="@style/Theme.OPMS.NoActionBar" />
119-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:101:13-58
120
121        <!-- 个人信息编辑页面 -->
122        <activity
122-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:104:9-107:38
123            android:name="com.opms.ui.profile.ProfileEditActivity"
123-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:105:13-59
124            android:exported="false"
124-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:106:13-37
125            android:label="编辑个人信息" />
125-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:107:13-35
126
127        <!-- 修改密码页面 -->
128        <activity
128-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:110:9-113:36
129            android:name="com.opms.ui.profile.PasswordChangeActivity"
129-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:111:13-62
130            android:exported="false"
130-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:112:13-37
131            android:label="修改密码" />
131-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:113:13-33
132
133        <!-- 用户管理页面 -->
134        <activity
134-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:116:9-119:36
135            android:name="com.opms.ui.system.UserManagementActivity"
135-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:117:13-61
136            android:exported="false"
136-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:118:13-37
137            android:label="用户管理" />
137-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:119:13-33
138
139        <!-- 用户编辑页面 -->
140        <activity
140-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:122:9-125:38
141            android:name="com.opms.ui.system.UserEditActivity"
141-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:123:13-55
142            android:exported="false"
142-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:124:13-37
143            android:label="编辑用户信息" />
143-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:125:13-35
144
145        <!-- 部门管理页面 -->
146        <activity
146-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:128:9-132:61
147            android:name="com.opms.ui.system.DepartmentManagementActivity"
147-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:129:13-67
148            android:exported="false"
148-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:130:13-37
149            android:label="部门管理"
149-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:131:13-33
150            android:theme="@style/Theme.OPMS.NoActionBar" />
150-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:132:13-58
151
152        <!-- 职位管理页面 -->
153        <activity
153-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:135:9-139:61
154            android:name="com.opms.ui.system.PositionManagementActivity"
154-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:136:13-65
155            android:exported="false"
155-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:137:13-37
156            android:label="职位管理"
156-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:138:13-33
157            android:theme="@style/Theme.OPMS.NoActionBar" />
157-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:139:13-58
158
159        <!-- 职位编辑页面 -->
160        <activity
160-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:142:9-146:61
161            android:name="com.opms.ui.system.PositionEditActivity"
161-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:143:13-59
162            android:exported="false"
162-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:144:13-37
163            android:label="职位编辑"
163-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:145:13-33
164            android:theme="@style/Theme.OPMS.NoActionBar" />
164-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:146:13-58
165
166        <!-- 岗位管理页面 -->
167        <activity
167-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:149:9-153:61
168            android:name="com.opms.ui.system.PostManagementActivity"
168-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:150:13-61
169            android:exported="false"
169-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:151:13-37
170            android:label="岗位管理"
170-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:152:13-33
171            android:theme="@style/Theme.OPMS.NoActionBar" />
171-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:153:13-58
172
173        <!-- 岗位编辑页面 -->
174        <activity
174-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:156:9-160:61
175            android:name="com.opms.ui.system.PostEditActivity"
175-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:157:13-55
176            android:exported="false"
176-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:158:13-37
177            android:label="岗位编辑"
177-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:159:13-33
178            android:theme="@style/Theme.OPMS.NoActionBar" />
178-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:160:13-58
179
180        <!-- 流程管理页面 -->
181        <activity
181-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:163:9-167:61
182            android:name="com.opms.ui.system.ProcessTemplateManagementActivity"
182-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:164:13-72
183            android:exported="false"
183-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:165:13-37
184            android:label="流程管理"
184-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:166:13-33
185            android:theme="@style/Theme.OPMS.NoActionBar" />
185-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:167:13-58
186
187        <!-- 流程编辑页面 -->
188        <activity
188-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:170:9-174:61
189            android:name="com.opms.ui.system.ProcessTemplateEditActivity"
189-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:171:13-66
190            android:exported="false"
190-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:172:13-37
191            android:label="流程编辑"
191-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:173:13-33
192            android:theme="@style/Theme.OPMS.NoActionBar" />
192-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:174:13-58
193
194        <!-- 权限管理页面 -->
195        <activity
195-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:177:9-181:61
196            android:name="com.opms.ui.system.PermissionManagementActivity"
196-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:178:13-67
197            android:exported="false"
197-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:179:13-37
198            android:label="权限管理"
198-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:180:13-33
199            android:theme="@style/Theme.OPMS.NoActionBar" />
199-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:181:13-58
200
201        <!-- 权限编辑页面 -->
202        <activity
202-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:184:9-188:61
203            android:name="com.opms.ui.system.PermissionEditActivity"
203-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:185:13-61
204            android:exported="false"
204-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:186:13-37
205            android:label="权限编辑"
205-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:187:13-33
206            android:theme="@style/Theme.OPMS.NoActionBar" />
206-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:188:13-58
207
208        <!-- 流程岗位映射管理页面 -->
209        <activity
209-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:191:9-195:61
210            android:name="com.opms.ui.system.ProcessPostMappingManagementActivity"
210-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:192:13-75
211            android:exported="false"
211-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:193:13-37
212            android:label="流程岗位映射"
212-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:194:13-35
213            android:theme="@style/Theme.OPMS.NoActionBar" />
213-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:195:13-58
214
215        <!-- 流程岗位映射编辑页面 -->
216        <activity
216-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:198:9-202:61
217            android:name="com.opms.ui.system.ProcessPostMappingEditActivity"
217-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:199:13-69
218            android:exported="false"
218-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:200:13-37
219            android:label="流程岗位映射编辑"
219-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:201:13-37
220            android:theme="@style/Theme.OPMS.NoActionBar" />
220-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:202:13-58
221
222        <!-- 用户审核列表页面 -->
223        <activity
223-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:205:9-209:61
224            android:name="com.opms.ui.system.UserAuditListActivity"
224-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:206:13-60
225            android:exported="false"
225-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:207:13-37
226            android:label="用户审核"
226-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:208:13-33
227            android:theme="@style/Theme.OPMS.NoActionBar" />
227-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:209:13-58
228
229        <!-- 用户审核详情页面 -->
230        <activity
230-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:212:9-216:61
231            android:name="com.opms.ui.system.UserAuditActivity"
231-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:213:13-56
232            android:exported="false"
232-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:214:13-37
233            android:label="用户审核"
233-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:215:13-33
234            android:theme="@style/Theme.OPMS.NoActionBar" />
234-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:216:13-58
235
236        <!-- 客户管理页面 -->
237        <activity
237-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:219:9-223:61
238            android:name="com.opms.ui.business.CustomerManagementActivity"
238-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:220:13-67
239            android:exported="false"
239-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:221:13-37
240            android:label="客户管理"
240-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:222:13-33
241            android:theme="@style/Theme.OPMS.NoActionBar" />
241-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:223:13-58
242
243        <!-- 客户编辑页面 -->
244        <activity
244-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:226:9-230:61
245            android:name="com.opms.ui.business.CustomerEditActivity"
245-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:227:13-61
246            android:exported="false"
246-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:228:13-37
247            android:label="客户编辑"
247-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:229:13-33
248            android:theme="@style/Theme.OPMS.NoActionBar" />
248-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:230:13-58
249
250        <!-- 产品管理页面 -->
251        <activity
251-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:233:9-237:61
252            android:name="com.opms.ui.business.ProductManagementActivity"
252-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:234:13-66
253            android:exported="false"
253-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:235:13-37
254            android:label="产品管理"
254-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:236:13-33
255            android:theme="@style/Theme.OPMS.NoActionBar" />
255-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:237:13-58
256
257        <!-- 产品编辑页面 -->
258        <activity
258-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:240:9-244:61
259            android:name="com.opms.ui.business.ProductEditActivity"
259-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:241:13-60
260            android:exported="false"
260-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:242:13-37
261            android:label="产品编辑"
261-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:243:13-33
262            android:theme="@style/Theme.OPMS.NoActionBar" />
262-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:244:13-58
263
264        <!-- 订单管理页面 -->
265        <activity
265-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:247:9-251:61
266            android:name="com.opms.ui.business.OrderManagementActivity"
266-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:248:13-64
267            android:exported="false"
267-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:249:13-37
268            android:label="订单管理"
268-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:250:13-33
269            android:theme="@style/Theme.OPMS.NoActionBar" />
269-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:251:13-58
270
271        <!-- 部件管理页面 -->
272        <activity
272-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:254:9-258:61
273            android:name="com.opms.ui.business.ComponentManagementActivity"
273-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:255:13-68
274            android:exported="false"
274-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:256:13-37
275            android:label="部件管理"
275-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:257:13-33
276            android:theme="@style/Theme.OPMS.NoActionBar" />
276-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:258:13-58
277
278        <!-- 部件编辑页面 -->
279        <activity
279-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:261:9-265:61
280            android:name="com.opms.ui.business.ComponentEditActivity"
280-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:262:13-62
281            android:exported="false"
281-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:263:13-37
282            android:label="部件编辑"
282-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:264:13-33
283            android:theme="@style/Theme.OPMS.NoActionBar" />
283-->E:\DataCode\OrderProductionManagementSystem\app\src\main\AndroidManifest.xml:265:13-58
284
285        <provider
285-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
286            android:name="androidx.startup.InitializationProvider"
286-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
287            android:authorities="com.opms.androidx-startup"
287-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
288            android:exported="false" >
288-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
289            <meta-data
289-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
290                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
290-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
291                android:value="androidx.startup" />
291-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Android\GradleRepository\caches\8.11.1\transforms\d2d730939c9267fb637b0c082d2bc5d3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
292            <meta-data
292-->[androidx.emoji2:emoji2:1.2.0] D:\Android\GradleRepository\caches\8.11.1\transforms\99c1f6faa94b27b88ccd35ada499447a\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
293                android:name="androidx.emoji2.text.EmojiCompatInitializer"
293-->[androidx.emoji2:emoji2:1.2.0] D:\Android\GradleRepository\caches\8.11.1\transforms\99c1f6faa94b27b88ccd35ada499447a\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
294                android:value="androidx.startup" />
294-->[androidx.emoji2:emoji2:1.2.0] D:\Android\GradleRepository\caches\8.11.1\transforms\99c1f6faa94b27b88ccd35ada499447a\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
295            <meta-data
295-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
296                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
296-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
297                android:value="androidx.startup" />
297-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
298        </provider>
299
300        <uses-library
300-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
301            android:name="androidx.window.extensions"
301-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
302            android:required="false" />
302-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
303        <uses-library
303-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
304            android:name="androidx.window.sidecar"
304-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
305            android:required="false" />
305-->[androidx.window:window:1.0.0] D:\Android\GradleRepository\caches\8.11.1\transforms\a71b722315b7fe0b1a8423709a320bad\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
306
307        <service
307-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
308            android:name="androidx.room.MultiInstanceInvalidationService"
308-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
309            android:directBootAware="true"
309-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
310            android:exported="false" />
310-->[androidx.room:room-runtime:2.6.1] D:\Android\GradleRepository\caches\8.11.1\transforms\145b37cfffdd8979d8b3e20a1994d707\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
311
312        <receiver
312-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
313            android:name="androidx.profileinstaller.ProfileInstallReceiver"
313-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
314            android:directBootAware="false"
314-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
315            android:enabled="true"
315-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
316            android:exported="true"
316-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
317            android:permission="android.permission.DUMP" >
317-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
318            <intent-filter>
318-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
319                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
319-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
319-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
320            </intent-filter>
321            <intent-filter>
321-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
322                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
322-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
322-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
323            </intent-filter>
324            <intent-filter>
324-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
325                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
325-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
325-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
326            </intent-filter>
327            <intent-filter>
327-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
328                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
328-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
328-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.11.1\transforms\cfd4bb75965d112519d8f3b02d9e04c6\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
329            </intent-filter>
330        </receiver>
331    </application>
332
333</manifest>
