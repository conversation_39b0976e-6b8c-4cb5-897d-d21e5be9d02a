<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_post_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_post_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_post_edit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="166" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/til_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="35" startOffset="12" endLine="51" endOffset="67"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="16" endLine="49" endOffset="44"/></Target><Target id="@+id/til_code" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="54" startOffset="12" endLine="70" endOffset="67"/></Target><Target id="@+id/et_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="16" endLine="68" endOffset="44"/></Target><Target id="@+id/switch_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="111" startOffset="20" endLine="115" endOffset="48"/></Target><Target id="@+id/ll_buttons" view="LinearLayout"><Expressions/><location startLine="137" startOffset="4" endLine="164" endOffset="18"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="147" startOffset="8" endLine="154" endOffset="31"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="156" startOffset="8" endLine="162" endOffset="31"/></Target></Targets></Layout>