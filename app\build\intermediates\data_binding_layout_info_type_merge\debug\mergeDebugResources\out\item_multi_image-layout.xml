<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_multi_image" modulePackage="com.opms" filePath="app\src\main\res\layout\item_multi_image.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/item_multi_image_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="58" endOffset="13"/></Target><Target id="@+id/card_image" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="7" startOffset="4" endLine="41" endOffset="55"/></Target><Target id="@+id/iv_image" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="22" endOffset="58"/></Target><Target id="@+id/view_uploading_overlay" view="View"><Expressions/><location startLine="25" startOffset="8" endLine="30" endOffset="39"/></Target><Target id="@+id/progress_uploading" view="ProgressBar"><Expressions/><location startLine="33" startOffset="8" endLine="39" endOffset="39"/></Target><Target id="@+id/btn_delete" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="44" startOffset="4" endLine="56" endOffset="35"/></Target></Targets></Layout>