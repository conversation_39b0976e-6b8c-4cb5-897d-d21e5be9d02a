<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="com.opms" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_profile_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="289" endOffset="39"/></Target><Target id="@+id/cardAvatar" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="12" startOffset="8" endLine="28" endOffset="43"/></Target><Target id="@+id/ivAvatar" view="ImageView"><Expressions/><location startLine="23" startOffset="12" endLine="27" endOffset="48"/></Target><Target id="@+id/tvDisplayUsername" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="41" endOffset="66"/></Target><Target id="@+id/tilUsername" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="43" startOffset="8" endLine="57" endOffset="63"/></Target><Target id="@+id/etUsername" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="52" startOffset="12" endLine="56" endOffset="41"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="59" startOffset="8" endLine="72" endOffset="63"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="68" startOffset="12" endLine="71" endOffset="54"/></Target><Target id="@+id/tilGender" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="74" startOffset="8" endLine="88" endOffset="63"/></Target><Target id="@+id/actGender" view="AutoCompleteTextView"><Expressions/><location startLine="83" startOffset="12" endLine="87" endOffset="42"/></Target><Target id="@+id/tilBirthday" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="90" startOffset="8" endLine="105" endOffset="63"/></Target><Target id="@+id/etBirthday" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="99" startOffset="12" endLine="104" endOffset="42"/></Target><Target id="@+id/tilIdCard" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="107" startOffset="8" endLine="120" endOffset="63"/></Target><Target id="@+id/etIdCard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="116" startOffset="12" endLine="119" endOffset="54"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="122" startOffset="8" endLine="136" endOffset="63"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="131" startOffset="12" endLine="135" endOffset="43"/></Target><Target id="@+id/tilEmployeeId" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="138" startOffset="8" endLine="151" endOffset="63"/></Target><Target id="@+id/etEmployeeId" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="147" startOffset="12" endLine="150" endOffset="54"/></Target><Target id="@+id/tilRole" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="153" startOffset="8" endLine="167" endOffset="63"/></Target><Target id="@+id/actRole" view="AutoCompleteTextView"><Expressions/><location startLine="162" startOffset="12" endLine="166" endOffset="42"/></Target><Target id="@+id/tilDepartment" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="169" startOffset="8" endLine="183" endOffset="63"/></Target><Target id="@+id/actDepartment" view="AutoCompleteTextView"><Expressions/><location startLine="178" startOffset="12" endLine="182" endOffset="42"/></Target><Target id="@+id/tilPosition" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="185" startOffset="8" endLine="199" endOffset="63"/></Target><Target id="@+id/actPosition" view="AutoCompleteTextView"><Expressions/><location startLine="194" startOffset="12" endLine="198" endOffset="42"/></Target><Target id="@+id/tilJob" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="201" startOffset="8" endLine="215" endOffset="63"/></Target><Target id="@+id/actJob" view="AutoCompleteTextView"><Expressions/><location startLine="210" startOffset="12" endLine="214" endOffset="42"/></Target><Target id="@+id/tilPermissionTemplate" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="217" startOffset="8" endLine="231" endOffset="63"/></Target><Target id="@+id/actPermissionTemplate" view="AutoCompleteTextView"><Expressions/><location startLine="226" startOffset="12" endLine="230" endOffset="42"/></Target><Target id="@+id/tilRemark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="233" startOffset="8" endLine="249" endOffset="63"/></Target><Target id="@+id/etRemark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="242" startOffset="12" endLine="248" endOffset="35"/></Target><Target id="@+id/tilRegisterTime" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="251" startOffset="8" endLine="265" endOffset="63"/></Target><Target id="@+id/etRegisterTime" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="260" startOffset="12" endLine="264" endOffset="41"/></Target><Target id="@+id/btnEdit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="267" startOffset="8" endLine="276" endOffset="71"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="278" startOffset="8" endLine="286" endOffset="60"/></Target></Targets></Layout>