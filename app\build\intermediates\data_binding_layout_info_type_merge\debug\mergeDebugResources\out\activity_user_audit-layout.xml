<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_audit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_user_audit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_user_audit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="385" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="39" startOffset="8" endLine="381" endOffset="22"/></Target><Target id="@+id/include_user_assignment" tag="binding_1" include="layout_user_assignment"><Expressions/><location startLine="364" startOffset="12" endLine="368" endOffset="54"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/progress_loading" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="22" startOffset="4" endLine="28" endOffset="59"/></Target><Target id="@+id/scroll_content" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="31" startOffset="4" endLine="383" endOffset="43"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="82" startOffset="28" endLine="88" endOffset="66"/></Target><Target id="@+id/tv_username" view="TextView"><Expressions/><location startLine="113" startOffset="32" endLine="121" endOffset="58"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="139" startOffset="32" endLine="146" endOffset="53"/></Target><Target id="@+id/tv_gender" view="TextView"><Expressions/><location startLine="164" startOffset="32" endLine="171" endOffset="52"/></Target><Target id="@+id/tv_birth_date" view="TextView"><Expressions/><location startLine="200" startOffset="28" endLine="207" endOffset="57"/></Target><Target id="@+id/tv_id_card" view="TextView"><Expressions/><location startLine="225" startOffset="28" endLine="232" endOffset="65"/></Target><Target id="@+id/tv_phone" view="TextView"><Expressions/><location startLine="250" startOffset="28" endLine="257" endOffset="58"/></Target><Target id="@+id/tv_register_time" view="TextView"><Expressions/><location startLine="274" startOffset="28" endLine="281" endOffset="66"/></Target><Target id="@+id/card_audit_result" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="292" startOffset="12" endLine="361" endOffset="63"/></Target><Target id="@+id/chip_group_audit_result" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="317" startOffset="20" endLine="339" endOffset="64"/></Target><Target id="@+id/chip_approve" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="325" startOffset="24" endLine="330" endOffset="47"/></Target><Target id="@+id/chip_reject" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="332" startOffset="24" endLine="337" endOffset="47"/></Target><Target id="@+id/til_remark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="342" startOffset="20" endLine="357" endOffset="75"/></Target><Target id="@+id/et_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="349" startOffset="24" endLine="355" endOffset="50"/></Target><Target id="@+id/btn_submit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="371" startOffset="12" endLine="379" endOffset="41"/></Target></Targets></Layout>