package com.opms.ui.business.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.card.MaterialCardView;
import com.opms.R;
import com.opms.data.model.response.ProductResponse;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品列表适配器
 */
public class ProductAdapter extends RecyclerView.Adapter<ProductAdapter.ProductViewHolder> {

    private List<ProductResponse> productList = new ArrayList<>();
    private Context context;
    private OnProductClickListener listener;

    public ProductAdapter(Context context) {
        this.context = context;
    }

    public void setProductList(List<ProductResponse> productList) {
        this.productList = productList != null ? productList : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void addProducts(List<ProductResponse> products) {
        if (products != null && !products.isEmpty()) {
            int startPosition = this.productList.size();
            this.productList.addAll(products);
            notifyItemRangeInserted(startPosition, products.size());
        }
    }

    public void setOnProductClickListener(OnProductClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ProductViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_product, parent, false);
        return new ProductViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProductViewHolder holder, int position) {
        ProductResponse product = productList.get(position);
        holder.bind(product);
    }

    @Override
    public int getItemCount() {
        return productList.size();
    }

    public interface OnProductClickListener {
        void onProductClick(ProductResponse product);

        void onEditClick(ProductResponse product);

        void onDeleteClick(ProductResponse product);
    }

    public class ProductViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private ImageView ivProductImage;
        private TextView tvProductName;
        private TextView tvProductCode;
        private TextView tvProductModel;
        private TextView tvProductStandard;
        private TextView tvProductPrice;
        private TextView tvProductStatus;
        private View btnEdit;
        private View btnDelete;

        public ProductViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_product);
            ivProductImage = itemView.findViewById(R.id.iv_product_image);
            tvProductName = itemView.findViewById(R.id.tv_product_name);
            tvProductCode = itemView.findViewById(R.id.tv_product_code);
            tvProductModel = itemView.findViewById(R.id.tv_product_model);
            tvProductStandard = itemView.findViewById(R.id.tv_product_standard);
            tvProductPrice = itemView.findViewById(R.id.tv_product_price);
            tvProductStatus = itemView.findViewById(R.id.tv_product_status);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }

        public void bind(ProductResponse product) {
            // 设置产品名称
            tvProductName.setText(product.getName());

            // 设置产品编号
            tvProductCode.setText("编号: " + product.getCode());

            // 设置型号
            if (!TextUtils.isEmpty(product.getModel())) {
                tvProductModel.setText("型号: " + product.getModel());
                tvProductModel.setVisibility(View.VISIBLE);
            } else {
                tvProductModel.setVisibility(View.GONE);
            }

            // 设置规格
            if (!TextUtils.isEmpty(product.getStandard())) {
                tvProductStandard.setText("规格: " + product.getStandard());
                tvProductStandard.setVisibility(View.VISIBLE);
            } else {
                tvProductStandard.setVisibility(View.GONE);
            }

            // 设置价格
            DecimalFormat df = new DecimalFormat("#,##0.00");
            tvProductPrice.setText("￥" + df.format(product.getPrice()));

            // 设置状态
            if ("1".equals(product.getStatus())) {
                tvProductStatus.setText("启用");
                tvProductStatus.setBackgroundResource(R.drawable.bg_status_active);
            } else {
                tvProductStatus.setText("禁用");
                tvProductStatus.setBackgroundResource(R.drawable.bg_status_inactive);
            }

            // 加载产品图片
            if (!TextUtils.isEmpty(product.getImage())) {
                Glide.with(context)
                        .load(product.getImage())
                        .placeholder(R.drawable.ic_product_management)
                        .error(R.drawable.ic_product_management)
                        .into(ivProductImage);
            } else {
                ivProductImage.setImageResource(R.drawable.ic_product_management);
            }

            // 设置点击事件
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onProductClick(product);
                }
            });

            btnEdit.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onEditClick(product);
                }
            });

            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDeleteClick(product);
                }
            });
        }
    }
}
