<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="96" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="7" startOffset="4" endLine="15" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="17" startOffset="4" endLine="27" endOffset="59"/></Target><Target id="@+id/til_username" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="29" startOffset="4" endLine="45" endOffset="59"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="38" startOffset="8" endLine="43" endOffset="34"/></Target><Target id="@+id/til_password" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="47" startOffset="4" endLine="64" endOffset="59"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="8" endLine="62" endOffset="34"/></Target><Target id="@+id/btn_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="66" startOffset="4" endLine="73" endOffset="64"/></Target><Target id="@+id/btn_register" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="75" startOffset="4" endLine="84" endOffset="61"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="86" startOffset="4" endLine="94" endOffset="51"/></Target></Targets></Layout>