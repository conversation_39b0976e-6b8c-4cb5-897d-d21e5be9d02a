<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_user_assignment" modulePackage="com.opms" filePath="app\src\main\res\layout\layout_user_assignment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_user_assignment"><Targets><Target id="@+id/card_user_assignment" tag="layout/layout_user_assignment_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="131" endOffset="51"/></Target><Target id="@+id/til_employee_number" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="28" startOffset="8" endLine="43" endOffset="63"/></Target><Target id="@+id/et_employee_number" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="36" startOffset="12" endLine="41" endOffset="38"/></Target><Target id="@+id/til_role_type" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="46" startOffset="8" endLine="60" endOffset="63"/></Target><Target id="@+id/act_role_type" view="AutoCompleteTextView"><Expressions/><location startLine="54" startOffset="12" endLine="58" endOffset="42"/></Target><Target id="@+id/til_department" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="63" startOffset="8" endLine="77" endOffset="63"/></Target><Target id="@+id/act_department" view="AutoCompleteTextView"><Expressions/><location startLine="71" startOffset="12" endLine="75" endOffset="42"/></Target><Target id="@+id/til_position" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="80" startOffset="8" endLine="94" endOffset="63"/></Target><Target id="@+id/act_position" view="AutoCompleteTextView"><Expressions/><location startLine="88" startOffset="12" endLine="92" endOffset="42"/></Target><Target id="@+id/til_post" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="97" startOffset="8" endLine="111" endOffset="63"/></Target><Target id="@+id/act_post" view="AutoCompleteTextView"><Expressions/><location startLine="105" startOffset="12" endLine="109" endOffset="42"/></Target><Target id="@+id/til_permission_template" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="114" startOffset="8" endLine="127" endOffset="63"/></Target><Target id="@+id/act_permission_template" view="AutoCompleteTextView"><Expressions/><location startLine="121" startOffset="12" endLine="125" endOffset="42"/></Target></Targets></Layout>