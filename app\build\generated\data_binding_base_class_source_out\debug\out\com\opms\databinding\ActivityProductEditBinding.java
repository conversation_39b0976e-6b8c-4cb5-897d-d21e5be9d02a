// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProductEditBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnAddComponent;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final CardView cardBasicInfo;

  @NonNull
  public final CardView cardComponentInfo;

  @NonNull
  public final MaterialCardView cardImage;

  @NonNull
  public final TextInputEditText etProductCode;

  @NonNull
  public final TextInputEditText etProductModel;

  @NonNull
  public final TextInputEditText etProductName;

  @NonNull
  public final TextInputEditText etProductPrice;

  @NonNull
  public final TextInputEditText etProductRemark;

  @NonNull
  public final TextInputEditText etProductStandard;

  @NonNull
  public final ImageView ivProductImage;

  @NonNull
  public final LinearLayout llComponentEmpty;

  @NonNull
  public final LinearLayout llComponentList;

  @NonNull
  public final LinearLayout llImageContainer;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final AutoCompleteTextView spinnerStatus;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvImageHint;

  @NonNull
  public final View viewImageOverlay;

  private ActivityProductEditBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnAddComponent, @NonNull MaterialButton btnCancel,
      @NonNull MaterialButton btnSave, @NonNull CardView cardBasicInfo,
      @NonNull CardView cardComponentInfo, @NonNull MaterialCardView cardImage,
      @NonNull TextInputEditText etProductCode, @NonNull TextInputEditText etProductModel,
      @NonNull TextInputEditText etProductName, @NonNull TextInputEditText etProductPrice,
      @NonNull TextInputEditText etProductRemark, @NonNull TextInputEditText etProductStandard,
      @NonNull ImageView ivProductImage, @NonNull LinearLayout llComponentEmpty,
      @NonNull LinearLayout llComponentList, @NonNull LinearLayout llImageContainer,
      @NonNull ProgressBar progressBar, @NonNull AutoCompleteTextView spinnerStatus,
      @NonNull Toolbar toolbar, @NonNull TextView tvImageHint, @NonNull View viewImageOverlay) {
    this.rootView = rootView;
    this.btnAddComponent = btnAddComponent;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.cardBasicInfo = cardBasicInfo;
    this.cardComponentInfo = cardComponentInfo;
    this.cardImage = cardImage;
    this.etProductCode = etProductCode;
    this.etProductModel = etProductModel;
    this.etProductName = etProductName;
    this.etProductPrice = etProductPrice;
    this.etProductRemark = etProductRemark;
    this.etProductStandard = etProductStandard;
    this.ivProductImage = ivProductImage;
    this.llComponentEmpty = llComponentEmpty;
    this.llComponentList = llComponentList;
    this.llImageContainer = llImageContainer;
    this.progressBar = progressBar;
    this.spinnerStatus = spinnerStatus;
    this.toolbar = toolbar;
    this.tvImageHint = tvImageHint;
    this.viewImageOverlay = viewImageOverlay;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProductEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProductEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_product_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProductEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_component;
      MaterialButton btnAddComponent = ViewBindings.findChildViewById(rootView, id);
      if (btnAddComponent == null) {
        break missingId;
      }

      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.card_basic_info;
      CardView cardBasicInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardBasicInfo == null) {
        break missingId;
      }

      id = R.id.card_component_info;
      CardView cardComponentInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardComponentInfo == null) {
        break missingId;
      }

      id = R.id.card_image;
      MaterialCardView cardImage = ViewBindings.findChildViewById(rootView, id);
      if (cardImage == null) {
        break missingId;
      }

      id = R.id.et_product_code;
      TextInputEditText etProductCode = ViewBindings.findChildViewById(rootView, id);
      if (etProductCode == null) {
        break missingId;
      }

      id = R.id.et_product_model;
      TextInputEditText etProductModel = ViewBindings.findChildViewById(rootView, id);
      if (etProductModel == null) {
        break missingId;
      }

      id = R.id.et_product_name;
      TextInputEditText etProductName = ViewBindings.findChildViewById(rootView, id);
      if (etProductName == null) {
        break missingId;
      }

      id = R.id.et_product_price;
      TextInputEditText etProductPrice = ViewBindings.findChildViewById(rootView, id);
      if (etProductPrice == null) {
        break missingId;
      }

      id = R.id.et_product_remark;
      TextInputEditText etProductRemark = ViewBindings.findChildViewById(rootView, id);
      if (etProductRemark == null) {
        break missingId;
      }

      id = R.id.et_product_standard;
      TextInputEditText etProductStandard = ViewBindings.findChildViewById(rootView, id);
      if (etProductStandard == null) {
        break missingId;
      }

      id = R.id.iv_product_image;
      ImageView ivProductImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProductImage == null) {
        break missingId;
      }

      id = R.id.ll_component_empty;
      LinearLayout llComponentEmpty = ViewBindings.findChildViewById(rootView, id);
      if (llComponentEmpty == null) {
        break missingId;
      }

      id = R.id.ll_component_list;
      LinearLayout llComponentList = ViewBindings.findChildViewById(rootView, id);
      if (llComponentList == null) {
        break missingId;
      }

      id = R.id.ll_image_container;
      LinearLayout llImageContainer = ViewBindings.findChildViewById(rootView, id);
      if (llImageContainer == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.spinner_status;
      AutoCompleteTextView spinnerStatus = ViewBindings.findChildViewById(rootView, id);
      if (spinnerStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_image_hint;
      TextView tvImageHint = ViewBindings.findChildViewById(rootView, id);
      if (tvImageHint == null) {
        break missingId;
      }

      id = R.id.view_image_overlay;
      View viewImageOverlay = ViewBindings.findChildViewById(rootView, id);
      if (viewImageOverlay == null) {
        break missingId;
      }

      return new ActivityProductEditBinding((CoordinatorLayout) rootView, btnAddComponent,
          btnCancel, btnSave, cardBasicInfo, cardComponentInfo, cardImage, etProductCode,
          etProductModel, etProductName, etProductPrice, etProductRemark, etProductStandard,
          ivProductImage, llComponentEmpty, llComponentList, llImageContainer, progressBar,
          spinnerStatus, toolbar, tvImageHint, viewImageOverlay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
