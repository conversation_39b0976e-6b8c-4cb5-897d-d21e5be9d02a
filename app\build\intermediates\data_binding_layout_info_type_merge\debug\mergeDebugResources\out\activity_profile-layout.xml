<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_profile" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_profile_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="155" endOffset="51"/></Target><Target id="@+id/iv_avatar" view="ImageView"><Expressions/><location startLine="18" startOffset="12" endLine="25" endOffset="56"/></Target><Target id="@+id/tv_username" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="34" endOffset="42"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="49" startOffset="20" endLine="54" endOffset="49"/></Target><Target id="@+id/tv_gender" view="TextView"><Expressions/><location startLine="56" startOffset="20" endLine="61" endOffset="49"/></Target><Target id="@+id/tv_birthday" view="TextView"><Expressions/><location startLine="63" startOffset="20" endLine="68" endOffset="49"/></Target><Target id="@+id/tv_id_card" view="TextView"><Expressions/><location startLine="70" startOffset="20" endLine="75" endOffset="49"/></Target><Target id="@+id/tv_phone" view="TextView"><Expressions/><location startLine="77" startOffset="20" endLine="82" endOffset="49"/></Target><Target id="@+id/tv_employee_id" view="TextView"><Expressions/><location startLine="84" startOffset="20" endLine="89" endOffset="49"/></Target><Target id="@+id/tv_role" view="TextView"><Expressions/><location startLine="91" startOffset="20" endLine="96" endOffset="49"/></Target><Target id="@+id/tv_department" view="TextView"><Expressions/><location startLine="98" startOffset="20" endLine="103" endOffset="49"/></Target><Target id="@+id/tv_position" view="TextView"><Expressions/><location startLine="105" startOffset="20" endLine="110" endOffset="49"/></Target><Target id="@+id/tv_job" view="TextView"><Expressions/><location startLine="112" startOffset="20" endLine="117" endOffset="49"/></Target><Target id="@+id/tv_permission_template" view="TextView"><Expressions/><location startLine="119" startOffset="20" endLine="124" endOffset="49"/></Target><Target id="@+id/tv_remark" view="TextView"><Expressions/><location startLine="126" startOffset="20" endLine="131" endOffset="49"/></Target><Target id="@+id/tv_register_time" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="137" endOffset="49"/></Target><Target id="@+id/btn_logout" view="Button"><Expressions/><location startLine="147" startOffset="4" endLine="153" endOffset="57"/></Target></Targets></Layout>