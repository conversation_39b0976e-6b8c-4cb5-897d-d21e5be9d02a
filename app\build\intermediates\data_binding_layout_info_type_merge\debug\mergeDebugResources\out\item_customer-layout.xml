<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_customer" modulePackage="com.opms" filePath="app\src\main\res\layout\item_customer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_customer"><Targets><Target id="@+id/card_customer" tag="layout/item_customer_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="180" endOffset="51"/></Target><Target id="@+id/iv_customer_image" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="28" endOffset="55"/></Target><Target id="@+id/tv_customer_name" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="44" endOffset="71"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="58" endOffset="71"/></Target><Target id="@+id/tv_customer_code" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="74" endOffset="73"/></Target><Target id="@+id/tv_company_name" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="90" endOffset="73"/></Target><Target id="@+id/tv_contact" view="TextView"><Expressions/><location startLine="93" startOffset="8" endLine="106" endOffset="74"/></Target><Target id="@+id/tv_phone" view="TextView"><Expressions/><location startLine="109" startOffset="8" endLine="121" endOffset="74"/></Target><Target id="@+id/tv_address" view="TextView"><Expressions/><location startLine="124" startOffset="8" endLine="136" endOffset="67"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="149" startOffset="12" endLine="156" endOffset="46"/></Target><Target id="@+id/btn_delete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="158" startOffset="12" endLine="166" endOffset="45"/></Target><Target id="@+id/guideline" view="androidx.constraintlayout.widget.Guideline"><Expressions/><location startLine="171" startOffset="8" endLine="176" endOffset="54"/></Target></Targets></Layout>