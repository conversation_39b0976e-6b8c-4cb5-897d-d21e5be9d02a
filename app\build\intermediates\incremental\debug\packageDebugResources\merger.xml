<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res"><file name="bottom_nav_item_anim" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\bottom_nav_item_anim.xml" qualifiers="" type="anim"/><file name="fade_in" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="slide_down" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\slide_down.xml" qualifiers="" type="anim"/><file name="slide_in_left" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="bg_circle" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_circle.xml" qualifiers="" type="drawable"/><file name="bg_gender_badge" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_gender_badge.xml" qualifiers="" type="drawable"/><file name="bg_icon_circle" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_icon_circle.xml" qualifiers="" type="drawable"/><file name="bg_icon_circle_small" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_icon_circle_small.xml" qualifiers="" type="drawable"/><file name="bg_image_overlay" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_image_overlay.xml" qualifiers="" type="drawable"/><file name="bg_image_placeholder" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_image_placeholder.xml" qualifiers="" type="drawable"/><file name="bg_info_card" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_info_card.xml" qualifiers="" type="drawable"/><file name="bg_item_normal" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_item_normal.xml" qualifiers="" type="drawable"/><file name="bg_item_selected" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_item_selected.xml" qualifiers="" type="drawable"/><file name="bg_menu_item" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_menu_item.xml" qualifiers="" type="drawable"/><file name="bg_sequence_badge" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_sequence_badge.xml" qualifiers="" type="drawable"/><file name="bg_spinner" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_spinner.xml" qualifiers="" type="drawable"/><file name="bg_spinner_with_arrow" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_spinner_with_arrow.xml" qualifiers="" type="drawable"/><file name="bg_status_active" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_status_active.xml" qualifiers="" type="drawable"/><file name="bg_status_inactive" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_status_inactive.xml" qualifiers="" type="drawable"/><file name="bg_status_pending" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_status_pending.xml" qualifiers="" type="drawable"/><file name="bg_status_unknown" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_status_unknown.xml" qualifiers="" type="drawable"/><file name="bg_type_api" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_type_api.xml" qualifiers="" type="drawable"/><file name="bg_type_button" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_type_button.xml" qualifiers="" type="drawable"/><file name="bg_type_menu" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_type_menu.xml" qualifiers="" type="drawable"/><file name="bg_type_other" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\bg_type_other.xml" qualifiers="" type="drawable"/><file name="circle_green" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\circle_green.xml" qualifiers="" type="drawable"/><file name="circle_red" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\circle_red.xml" qualifiers="" type="drawable"/><file name="default_avatar" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\default_avatar.xml" qualifiers="" type="drawable"/><file name="ic_add" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_api" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_api.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_drop_down" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_arrow_drop_down.xml" qualifiers="" type="drawable"/><file name="ic_assignment" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_assignment.xml" qualifiers="" type="drawable"/><file name="ic_audit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_audit.xml" qualifiers="" type="drawable"/><file name="ic_avatar_placeholder" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_avatar_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_business" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_business.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_code" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_code.xml" qualifiers="" type="drawable"/><file name="ic_component_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_component_management.xml" qualifiers="" type="drawable"/><file name="ic_customer_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_customer_management.xml" qualifiers="" type="drawable"/><file name="ic_default_avatar" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_default_avatar.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_department_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_department_management.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_expand_less" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_expand_less.xml" qualifiers="" type="drawable"/><file name="ic_expand_more" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_expand_more.xml" qualifiers="" type="drawable"/><file name="ic_job_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_job_management.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_model" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_model.xml" qualifiers="" type="drawable"/><file name="ic_order_decomposition" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_order_decomposition.xml" qualifiers="" type="drawable"/><file name="ic_order_entry" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_order_entry.xml" qualifiers="" type="drawable"/><file name="ic_order_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_order_management.xml" qualifiers="" type="drawable"/><file name="ic_order_scheduling" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_order_scheduling.xml" qualifiers="" type="drawable"/><file name="ic_permission_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_permission_management.xml" qualifiers="" type="drawable"/><file name="ic_person" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_position_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_position_management.xml" qualifiers="" type="drawable"/><file name="ic_process_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_process_management.xml" qualifiers="" type="drawable"/><file name="ic_process_post_mapping" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_process_post_mapping.xml" qualifiers="" type="drawable"/><file name="ic_production_tracking" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_production_tracking.xml" qualifiers="" type="drawable"/><file name="ic_product_inbound" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_product_inbound.xml" qualifiers="" type="drawable"/><file name="ic_product_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_product_management.xml" qualifiers="" type="drawable"/><file name="ic_product_outbound" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_product_outbound.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_quality_inspection" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_quality_inspection.xml" qualifiers="" type="drawable"/><file name="ic_remark" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_remark.xml" qualifiers="" type="drawable"/><file name="ic_save" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_save.xml" qualifiers="" type="drawable"/><file name="ic_search" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_security" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_security.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_standard" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_standard.xml" qualifiers="" type="drawable"/><file name="ic_system_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_system_management.xml" qualifiers="" type="drawable"/><file name="ic_todo" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_todo.xml" qualifiers="" type="drawable"/><file name="ic_touch_app" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_touch_app.xml" qualifiers="" type="drawable"/><file name="ic_user_audit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_user_audit.xml" qualifiers="" type="drawable"/><file name="ic_user_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_user_management.xml" qualifiers="" type="drawable"/><file name="ic_work" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\drawable\ic_work.xml" qualifiers="" type="drawable"/><file name="activity_avatar_view" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_avatar_view.xml" qualifiers="" type="layout"/><file name="activity_component_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_component_edit.xml" qualifiers="" type="layout"/><file name="activity_component_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_component_management.xml" qualifiers="" type="layout"/><file name="activity_customer_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_customer_edit.xml" qualifiers="" type="layout"/><file name="activity_customer_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_customer_management.xml" qualifiers="" type="layout"/><file name="activity_department_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_department_management.xml" qualifiers="" type="layout"/><file name="activity_login" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_order_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_order_management.xml" qualifiers="" type="layout"/><file name="activity_password_change" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_password_change.xml" qualifiers="" type="layout"/><file name="activity_permission_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_permission_edit.xml" qualifiers="" type="layout"/><file name="activity_permission_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_permission_management.xml" qualifiers="" type="layout"/><file name="activity_position_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_position_edit.xml" qualifiers="" type="layout"/><file name="activity_position_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_position_management.xml" qualifiers="" type="layout"/><file name="activity_post_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_post_edit.xml" qualifiers="" type="layout"/><file name="activity_post_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_post_management.xml" qualifiers="" type="layout"/><file name="activity_process_post_mapping_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_process_post_mapping_edit.xml" qualifiers="" type="layout"/><file name="activity_process_post_mapping_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_process_post_mapping_management.xml" qualifiers="" type="layout"/><file name="activity_process_template_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_process_template_edit.xml" qualifiers="" type="layout"/><file name="activity_process_template_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_process_template_management.xml" qualifiers="" type="layout"/><file name="activity_product_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_product_edit.xml" qualifiers="" type="layout"/><file name="activity_product_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_product_management.xml" qualifiers="" type="layout"/><file name="activity_profile" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_profile.xml" qualifiers="" type="layout"/><file name="activity_profile_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_profile_edit.xml" qualifiers="" type="layout"/><file name="activity_register" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_register.xml" qualifiers="" type="layout"/><file name="activity_registration_detail" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_registration_detail.xml" qualifiers="" type="layout"/><file name="activity_splash" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_user_audit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_user_audit.xml" qualifiers="" type="layout"/><file name="activity_user_audit_list" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_user_audit_list.xml" qualifiers="" type="layout"/><file name="activity_user_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_user_edit.xml" qualifiers="" type="layout"/><file name="activity_user_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\activity_user_management.xml" qualifiers="" type="layout"/><file name="dialog_department_edit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\dialog_department_edit.xml" qualifiers="" type="layout"/><file name="fragment_business" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_business.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_profile_compact" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_profile_compact.xml" qualifiers="" type="layout"/><file name="fragment_profile_new" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_profile_new.xml" qualifiers="" type="layout"/><file name="fragment_system_manage" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_system_manage.xml" qualifiers="" type="layout"/><file name="fragment_system_management" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_system_management.xml" qualifiers="" type="layout"/><file name="fragment_todo" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_todo.xml" qualifiers="" type="layout"/><file name="fragment_user_audit" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\fragment_user_audit.xml" qualifiers="" type="layout"/><file name="item_business_module_checkbox" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_business_module_checkbox.xml" qualifiers="" type="layout"/><file name="item_component" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_component.xml" qualifiers="" type="layout"/><file name="item_customer" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_customer.xml" qualifiers="" type="layout"/><file name="item_department_tree" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_department_tree.xml" qualifiers="" type="layout"/><file name="item_loading" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_loading.xml" qualifiers="" type="layout"/><file name="item_pending_user" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_pending_user.xml" qualifiers="" type="layout"/><file name="item_permission" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_permission.xml" qualifiers="" type="layout"/><file name="item_position" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_position.xml" qualifiers="" type="layout"/><file name="item_post" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_post.xml" qualifiers="" type="layout"/><file name="item_process_post_mapping" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_process_post_mapping.xml" qualifiers="" type="layout"/><file name="item_process_template" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_process_template.xml" qualifiers="" type="layout"/><file name="item_product" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_product.xml" qualifiers="" type="layout"/><file name="item_product_component" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_product_component.xml" qualifiers="" type="layout"/><file name="item_registration" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_registration.xml" qualifiers="" type="layout"/><file name="item_user" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\item_user.xml" qualifiers="" type="layout"/><file name="layout_user_assignment" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\layout\layout_user_assignment.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="menu_bottom_nav_admin" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\menu\menu_bottom_nav_admin.xml" qualifiers="" type="menu"/><file name="menu_bottom_nav_user" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\menu\menu_bottom_nav_user.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph_admin" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\navigation\nav_graph_admin.xml" qualifiers="" type="navigation"/><file name="nav_graph_user" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\navigation\nav_graph_user.xml" qualifiers="" type="navigation"/><file path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#2196F3</color><color name="primary_dark">#1976D2</color><color name="primary_light">#BBDEFB</color><color name="primary_container">#E3F2FD</color><color name="primary_variant">#1976D2</color><color name="accent">#FF4081</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_hint">#BDBDBD</color><color name="text_white">#FFFFFF</color><color name="background">#F5F5F5</color><color name="surface">#FFFFFF</color><color name="surface_variant">#F0F0F0</color><color name="divider">#EEEEEE</color><color name="success">#4CAF50</color><color name="success_container">#E8F5E8</color><color name="warning">#FFC107</color><color name="warning_container">#FFF3E0</color><color name="error">#F44336</color><color name="error_container">#FFEBEE</color><color name="info">#2196F3</color><color name="info_container">#E3F2FD</color><color name="black">#000000</color><color name="white">#FFFFFF</color><color name="transparent">#00000000</color><color name="colorPrimary">@color/primary</color><color name="colorError">@color/error</color></file><file path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="text_size_small">12sp</dimen><dimen name="text_size_medium">14sp</dimen><dimen name="text_size_large">16sp</dimen><dimen name="text_size_xlarge">18sp</dimen><dimen name="text_size_xxlarge">20sp</dimen><dimen name="spacing_tiny">4dp</dimen><dimen name="spacing_small">8dp</dimen><dimen name="spacing_medium">16dp</dimen><dimen name="spacing_large">24dp</dimen><dimen name="spacing_xlarge">32dp</dimen><dimen name="button_height">54dp</dimen><dimen name="input_height">56dp</dimen><dimen name="toolbar_height">56dp</dimen><dimen name="bottom_nav_height">56dp</dimen><dimen name="icon_size_small">16dp</dimen><dimen name="icon_size_medium">24dp</dimen><dimen name="icon_size_large">32dp</dimen><dimen name="icon_size_xlarge">48dp</dimen><dimen name="corner_radius_small">4dp</dimen><dimen name="corner_radius_medium">8dp</dimen><dimen name="corner_radius_large">16dp</dimen><dimen name="elevation_small">2dp</dimen><dimen name="elevation_medium">4dp</dimen><dimen name="elevation_large">8dp</dimen><dimen name="avatar_size_small">32dp</dimen><dimen name="avatar_size_medium">48dp</dimen><dimen name="avatar_size_large">64dp</dimen><dimen name="avatar_size_xlarge">96dp</dimen><dimen name="list_item_height_small">48dp</dimen><dimen name="list_item_height_medium">64dp</dimen><dimen name="list_item_height_large">80dp</dimen><dimen name="image_size_small">64dp</dimen><dimen name="image_size_medium">96dp</dimen><dimen name="image_size_large">128dp</dimen><dimen name="image_size_xlarge">192dp</dimen></file><file path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">订单生产管理系统</string><string name="ok">确定</string><string name="cancel">取消</string><string name="save">保存</string><string name="delete">删除</string><string name="edit">编辑</string><string name="search">搜索</string><string name="confirm">确认</string><string name="back">返回</string><string name="loading">加载中…</string><string name="network_error">网络错误，请检查网络连接</string><string name="server_error">服务器错误，请稍后重试</string><string name="error_username_empty">请输入用户名</string><string name="error_password_empty">请输入密码</string><string name="error_login_failed">登录失败，请检查用户名和密码</string><string name="error_network">网络连接失败，请稍后重试</string><string name="error_register_failed">注册失败，请稍后重试</string><string name="error_register_success">注册成功，请等待管理员审核</string><string name="error_username_length">用户名长度必须为6-20个字符</string><string name="error_password_length">密码长度必须为6-20个字符</string><string name="error_username_exists">用户名已存在，请选择其他用户名</string><string name="error_name_empty">请输入姓名</string><string name="error_birthday_empty">请选择出生日期</string><string name="error_id_card_empty">请输入身份证号码</string><string name="error_phone_empty">请输入电话号码</string><string name="error_gender_empty">请选择性别</string><string name="checking_username">正在检查用户名...</string><string name="username_available">用户名可用</string><string name="login">登录</string><string name="register">注册</string><string name="username">用户名</string><string name="password">密码</string><string name="confirm_password">确认密码</string><string name="real_name">姓名</string><string name="gender">性别</string><string name="male">男</string><string name="female">女</string><string name="birthday">出生年月日</string><string name="id_card">身份证号码</string><string name="phone">电话</string><string name="avatar">头像</string><string name="logout">退出登录</string><string name="choose_image">选择图片</string><string name="view_avatar">查看头像</string><string name="edit_avatar">编辑头像</string><string name="employee_id">工号</string><string name="role">角色</string><string name="job">岗位</string><string name="remark">备注</string><string name="register_time">注册时间</string><string name="user_audit">用户审核</string><string name="system_management">系统管理</string><string name="todo">待办事项</string><string name="business">业务处理</string><string name="me">我</string><string name="user_management">用户管理</string><string name="department_management">部门管理</string><string name="position_management">职位管理</string><string name="post_management">岗位管理</string><string name="process_management">流程管理</string><string name="permission_management">权限管理</string><string name="order_list">订单列表</string><string name="create_order">录入订单</string><string name="order_scheduling">订单排期</string><string name="order_follow_up">订单跟进</string><string name="order_confirmation">订单确认</string><string name="product_management">商品信息管理</string><string name="customer_management">客户管理</string><string name="order_number">订单编号</string><string name="order_date">订单日期</string><string name="order_amount">订单金额</string><string name="order_status">订单状态</string><string name="order_remark">订单备注</string><string name="delivery_date">约定交货日期</string><string name="customer_name">客户名称</string><string name="customer_code">客户编码</string><string name="product_name">商品名称</string><string name="product_code">商品编码</string><string name="product_quantity">商品数量</string><string name="product_image">商品图片</string><string name="product_model">型号</string><string name="product_specification">规格</string><string name="process_node">流程节点</string><string name="process_node_name">节点名称</string><string name="process_node_code">节点编码</string><string name="process_node_order">排序号</string><string name="process_node_status">节点状态</string><string name="cutting">下料</string><string name="stamping">冲压</string><string name="welding">焊接</string><string name="assembly">组装</string><string name="packaging">包装</string><string name="shipping">发货</string><string name="follow_up_status">跟进状态</string><string name="pending">待处理</string><string name="in_progress">处理中</string><string name="completed">已完成</string><string name="follow_up_record">跟进记录</string><string name="follow_up_content">跟进内容</string><string name="follow_up_time">跟进时间</string><string name="follow_up_person">跟进人</string><string name="department">部门</string><string name="department_name">部门名称</string><string name="department_code">部门编码</string><string name="parent_department">上级部门</string><string name="department_status">部门状态</string><string name="position">职位</string><string name="position_name">职位名称</string><string name="position_code">职位编码</string><string name="position_status">职位状态</string><string name="post">岗位</string><string name="post_name">岗位名称</string><string name="post_code">岗位编码</string><string name="post_status">岗位状态</string><string name="permission_template">权限模板</string><string name="template_name">模板名称</string><string name="template_code">模板编码</string><string name="template_status">模板状态</string><string name="business_module">业务模块</string></file><file path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.OPMS" parent="Theme.MaterialComponents.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>

        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/text_white</item>

        
        <item name="android:statusBarColor">@color/primary_dark</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>

        
        <item name="materialButtonStyle">@style/Widget.OPMS.Button</item>
        <item name="textInputStyle">@style/Widget.OPMS.TextInputLayout</item>
    </style><style name="Theme.OPMS.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Widget.OPMS.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/text_white</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="cornerRadius">4dp</item>
    </style><style name="Widget.OPMS.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
    </style><style name="Widget.OPMS.TextInputLayout.NoDropdownIcon" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="endIconMode">none</item>
    </style><style name="Widget.OPMS.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="contentPadding">16dp</item>
    </style><style name="Widget.OPMS.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
    </style></file><file path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.OrderProductionManagementSystem" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="E:\DataCode\OrderProductionManagementSystem\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\DataCode\OrderProductionManagementSystem\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\DataCode\OrderProductionManagementSystem\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\DataCode\OrderProductionManagementSystem\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\DataCode\OrderProductionManagementSystem\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>