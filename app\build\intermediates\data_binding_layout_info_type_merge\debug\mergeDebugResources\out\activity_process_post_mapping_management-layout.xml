<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_process_post_mapping_management" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_process_post_mapping_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_process_post_mapping_management_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="181" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="28"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="21" startOffset="4" endLine="29" endOffset="59"/></Target><Target id="@+id/btn_toggle_toolbar" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="32" startOffset="4" endLine="42" endOffset="33"/></Target><Target id="@+id/btn_toggle_search" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="45" startOffset="4" endLine="55" endOffset="33"/></Target><Target id="@+id/card_expandable_tools" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="58" startOffset="4" endLine="107" endOffset="55"/></Target><Target id="@+id/tv_mapping_count" view="TextView"><Expressions/><location startLine="77" startOffset="12" endLine="85" endOffset="42"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="96" startOffset="16" endLine="101" endOffset="42"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="110" startOffset="4" endLine="129" endOffset="59"/></Target><Target id="@+id/rv_mappings" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="118" startOffset="8" endLine="127" endOffset="64"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="132" startOffset="4" endLine="167" endOffset="18"/></Target><Target id="@+id/fab_add" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="170" startOffset="4" endLine="179" endOffset="33"/></Target></Targets></Layout>