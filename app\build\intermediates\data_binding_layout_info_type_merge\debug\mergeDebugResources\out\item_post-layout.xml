<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_post" modulePackage="com.opms" filePath="app\src\main\res\layout\item_post.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_post_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="183" endOffset="51"/></Target><Target id="@+id/ll_view_mode" view="LinearLayout"><Expressions/><location startLine="39" startOffset="12" endLine="86" endOffset="26"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="52" startOffset="20" endLine="60" endOffset="46"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="72" endOffset="58"/></Target><Target id="@+id/tv_code" view="TextView"><Expressions/><location startLine="77" startOffset="16" endLine="84" endOffset="42"/></Target><Target id="@+id/ll_edit_mode" view="LinearLayout"><Expressions/><location startLine="89" startOffset="12" endLine="163" endOffset="26"/></Target><Target id="@+id/til_edit_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="97" startOffset="16" endLine="112" endOffset="71"/></Target><Target id="@+id/et_edit_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="105" startOffset="20" endLine="110" endOffset="48"/></Target><Target id="@+id/switch_edit_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="128" startOffset="20" endLine="133" endOffset="57"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="141" startOffset="20" endLine="149" endOffset="49"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="152" startOffset="20" endLine="159" endOffset="49"/></Target><Target id="@+id/iv_delete" view="ImageView"><Expressions/><location startLine="168" startOffset="8" endLine="179" endOffset="37"/></Target></Targets></Layout>