<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_department_tree" modulePackage="com.opms" filePath="app\src\main\res\layout\item_department_tree.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_department_tree_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="80" endOffset="14"/></Target><Target id="@+id/view_indent" view="View"><Expressions/><location startLine="13" startOffset="4" endLine="16" endOffset="46"/></Target><Target id="@+id/iv_expand" view="ImageView"><Expressions/><location startLine="19" startOffset="4" endLine="30" endOffset="42"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="58" endOffset="30"/></Target><Target id="@+id/tv_code" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="67" endOffset="34"/></Target><Target id="@+id/view_selected_indicator" view="View"><Expressions/><location startLine="72" startOffset="4" endLine="78" endOffset="35"/></Target></Targets></Layout>