package com.opms.data.remote;

import com.opms.data.model.request.ChangePasswordRequest;
import com.opms.data.model.request.ComponentRequest;
import com.opms.data.model.request.CustomerRequest;
import com.opms.data.model.request.DepartmentRequest;
import com.opms.data.model.request.LoginRequest;
import com.opms.data.model.request.OrderRequest;
import com.opms.data.model.request.PermissionRequest;
import com.opms.data.model.request.PositionRequest;
import com.opms.data.model.request.PostRequest;
import com.opms.data.model.request.ProcessPostMappingRequest;
import com.opms.data.model.request.ProcessRequest;
import com.opms.data.model.request.ProcessTemplateRequest;
import com.opms.data.model.request.ProductRequest;
import com.opms.data.model.request.RegisterRequest;
import com.opms.data.model.request.UserAuditRequest;
import com.opms.data.model.request.UserRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.AvatarResponse;
import com.opms.data.model.response.ComponentListResponse;
import com.opms.data.model.response.ComponentResponse;
import com.opms.data.model.response.CustomerListResponse;
import com.opms.data.model.response.CustomerResponse;
import com.opms.data.model.response.DepartmentResponse;
import com.opms.data.model.response.LoginResponse;
import com.opms.data.model.response.OrderResponse;
import com.opms.data.model.response.PermissionCompleteResponse;
import com.opms.data.model.response.PermissionResponse;
import com.opms.data.model.response.PositionResponse;
import com.opms.data.model.response.PostResponse;
import com.opms.data.model.response.ProcessPostMappingListResponse;
import com.opms.data.model.response.ProcessPostMappingResponse;
import com.opms.data.model.response.ProcessResponse;
import com.opms.data.model.response.ProcessTemplateResponse;
import com.opms.data.model.response.ProductCompleteResponse;
import com.opms.data.model.response.ProductListResponse;
import com.opms.data.model.response.ProductResponse;
import com.opms.data.model.response.RegisterResponse;
import com.opms.data.model.response.UserListResponse;
import com.opms.data.model.response.UserResponse;

import java.util.List;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Part;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface ApiService {
    /**
     * 系统登录
     *
     * @param request LoginRequest
     * @return LoginResponse
     */
    @POST("auth/login")
    Call<ApiResponse<LoginResponse>> login(@Body LoginRequest request);

    /**
     * 注册（不上传图像）
     *
     * @param request RegisterRequest
     * @return RegisterResponse
     */
    @POST("auth/register")
    Call<ApiResponse<RegisterResponse>> register(@Body RegisterRequest request);

    /**
     * 用户列表》用户信息
     *
     * @param username String
     * @return UserResponse
     */
    @GET("auth/checkUsername")
    Call<ApiResponse<UserResponse>> checkUsernameExists(@Query("username") String username);

    /**
     * 注册，同时上传图像
     *
     * @param registerRequest RegisterRequest
     * @param imgRequest      RequestBody
     * @param avatar          MultipartBody.Part
     * @return RegisterResponse
     */
    @Multipart
    @POST("auth/registerWithAvatar")
    Call<ApiResponse<RegisterResponse>> registerWithAvatar(
            @Part("registerRequest") RegisterRequest registerRequest,
            @Part("imgRequest") RequestBody imgRequest,
            @Part MultipartBody.Part avatar);

    /**
     * 我的信息》获取信息
     *
     * @return UserResponse
     */
    @GET("api/user/profile")
    Call<ApiResponse<UserResponse>> getUserProfile();

    /**
     * 我的信息》获取图像
     *
     * @return AvatarResponse
     */
    @GET("api/user/avatar")
    Call<ApiResponse<AvatarResponse>> getUserAvatar();

    /**
     * 我的信息》修改
     *
     * @param request UserResponse
     * @return UserResponse
     */
    @POST("api/user/updateProfile")
    Call<ApiResponse<UserResponse>> updateUserProfile(@Body UserResponse request);

    /**
     * 我的信息》更改密码
     *
     * @param request ChangePasswordRequest
     * @return Void
     */
    @POST("api/user/changePassword")
    Call<ApiResponse<Void>> changePassword(@Body ChangePasswordRequest request);

    /**
     * 用户列表》用户信息
     *
     * @param username String
     * @return UserResponse
     */
    @GET("api/user/findByName")
    Call<ApiResponse<UserResponse>> findByUsername(@Query("username") String username);

    /**
     * 获取所有用户（分页）
     *
     * @param page    页码，从1开始
     * @param size    每页大小
     * @param keyword 搜索关键字（可选）
     * @return 用户列表
     */
    @GET("api/user/all")
    Call<ApiResponse<UserListResponse>> getUsers(
            @Query("page") int page,
            @Query("size") int size,
            @Query("keyword") String keyword);

    /**
     * 用户管理》更新用户信息
     *
     * @param username 用户名
     * @param request  RegisterRequest
     * @return UserResponse
     */
    @POST("api/user/updateUser")
    Call<ApiResponse<UserResponse>> updateUser(@Query("username") String username, @Body RegisterRequest request);

    /**
     * 用户管理》删除用户
     *
     * @param request 包含用户名的请求体
     * @return Void
     */
    @POST("api/user/delete")
    Call<ApiResponse<Void>> deleteUser(@Body UserRequest request);

    /**
     * 用户审核》获取用户审核列表
     *
     * @return
     */
    @GET("api/user/auditList")
    Call<ApiResponse<List<UserResponse>>> getAllAuditUsers();

    /**
     * 用户审核》获取用户审核列表
     *
     * @return
     */
    @GET("api/user/pendingList")
    Call<ApiResponse<List<UserResponse>>> getPendingUsers();

    /**
     * 用户审核》获取用户审核信息
     *
     * @return
     */
    @GET("api/user/detail")
    Call<ApiResponse<UserResponse>> getUserById(@Query("id") int id);

    /**
     * 用户审核》获取待审用户信息
     *
     * @param id
     * @return
     */
    @GET("api/user/pendingUser")
    Call<ApiResponse<UserResponse>> getPendingUserById(@Query("id") int id);

    /**
     * 用户审核》提交用户审核信息
     *
     * @param request
     * @return
     */
    @POST("api/user/auditUser")
    Call<ApiResponse<UserResponse>> auditUser(@Body UserAuditRequest request);

    /**
     * 获取下拉选项——部门
     *
     * @return List<DepartmentResponse>
     */
    @GET("departments")
    Call<ApiResponse<List<DepartmentResponse>>> getDepartments();

    /**
     * 获取下拉选项——职务
     *
     * @return List<PositionResponse>
     */
    @GET("positions")
    Call<ApiResponse<List<PositionResponse>>> getPositions();

    /**
     * 获取下拉选项——岗位
     *
     * @return List<PostResponse>
     */
    @GET("posts")
    Call<ApiResponse<List<PostResponse>>> getPosts();

    /**
     * 获取下拉选项——权限模板
     *
     * @return List<PermissionResponse>
     */
    @GET("permissions")
    Call<ApiResponse<List<PermissionResponse>>> getPermissions();

    /**
     * 部门管理》获取部门列表
     *
     * @return
     */
    @GET("api/department/list")
    Call<ApiResponse<List<DepartmentResponse>>> getDepartmentList();

    /**
     * 部门管理》新增部门
     *
     * @param request
     * @return
     */
    @POST("api/department/add")
    Call<ApiResponse<DepartmentResponse>> createDepartment(@Body DepartmentRequest request);

    /**
     * 部门管理》修改部门
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/department/update")
    Call<ApiResponse<DepartmentResponse>> updateDepartment(@Query("id") int id, @Body DepartmentRequest request);

    /**
     * 部门管理》删除部门
     *
     * @param request
     * @return
     */
    @POST("api/department/delete")
    Call<ApiResponse<Void>> deleteDepartment(@Body DepartmentRequest request);

    /**
     * 职务管理》新增职务
     *
     * @param request
     * @return
     */
    @POST("api/position/add")
    Call<ApiResponse<PositionResponse>> createPosition(@Body PositionRequest request);

    /**
     * 职务管理》修改职务
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/position/update")
    Call<ApiResponse<PositionResponse>> updatePosition(@Query("id") int id, @Body PositionRequest request);

    /**
     * 职务管理》删除职务
     *
     * @param request
     * @return
     */
    @POST("api/position/delete")
    Call<ApiResponse<Void>> deletePosition(@Body PositionRequest request);

    /**
     * 岗位管理》新增岗位
     *
     * @param request
     * @return
     */
    @POST("api/post/add")
    Call<ApiResponse<PostResponse>> createPost(@Body PostRequest request);

    /**
     * 岗位管理》修改岗位
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/post/update")
    Call<ApiResponse<PostResponse>> updatePost(@Query("id") int id, @Body PostRequest request);

    /**
     * 岗位管理》删除岗位
     *
     * @param request
     * @return
     */
    @POST("api/post/delete")
    Call<ApiResponse<Void>> deletePost(@Body PostRequest request);

    /**
     * 流程节点管理》获取流程节点列表
     *
     * @return
     */
    @GET("processes")
    Call<ApiResponse<List<ProcessTemplateResponse>>> getProcessTemplates();

    /**
     * 流程节点管理》获取流程节点详情
     *
     * @return
     */
    @GET("processes")
    Call<ApiResponse<List<ProcessResponse>>> getProcesses();

    /**
     * 流程节点管理》新增流程节点
     *
     * @param request
     * @return
     */
    @POST("api/process/add")
    Call<ApiResponse<ProcessResponse>> createProcess(@Body ProcessRequest request);

    /**
     * 流程节点管理》修改流程节点
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/process/update")
    Call<ApiResponse<ProcessResponse>> updateProcess(@Query("id") int id, @Body ProcessRequest request);

    /**
     * 流程节点管理》删除流程节点
     *
     * @param request
     * @return
     */
    @POST("api/process/delete")
    Call<ApiResponse<Void>> deleteProcess(@Body ProcessRequest request);

    /**
     * 流程节点管理》新增流程节点
     *
     * @param request
     * @return
     */
    @POST("api/process/add")
    Call<ApiResponse<ProcessTemplateResponse>> createProcessTemplate(@Body ProcessTemplateRequest request);

    /**
     * 流程节点管理》修改流程节点
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/process/update")
    Call<ApiResponse<ProcessTemplateResponse>> updateProcessTemplate(@Query("id") int id, @Body ProcessTemplateRequest request);

    /**
     * 流程节点管理》删除流程节点
     *
     * @param request
     * @return
     */
    @POST("api/process/delete")
    Call<ApiResponse<Void>> deleteProcessTemplate(@Body ProcessTemplateRequest request);

    /**
     * 权限模板管理》新增权限
     *
     * @param request
     * @return
     */
    // 权限相关接口
    @POST("api/permission/add")
    Call<ApiResponse<PermissionResponse>> createPermission(@Body PermissionRequest request);

    /**
     * 权限模板管理》获取权限模板详情
     *
     * @param id
     * @return
     */
    @GET("api/permission/detail")
    Call<ApiResponse<PermissionCompleteResponse>> getPermissionsById(@Query("id") int id);

    /**
     * 权限模板管理》修改权限模板
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/permission/update")
    Call<ApiResponse<PermissionResponse>> updatePermission(@Query("id") int id, @Body PermissionRequest request);

    /**
     * 权限模板管理》删除权限模板
     *
     * @param request
     * @return
     */
    @POST("api/permission/delete")
    Call<ApiResponse<Void>> deletePermission(@Body PermissionRequest request);

    /**
     * 客户管理》分页获取客户列表
     *
     * @param page
     * @param size
     * @param keyword
     * @return
     */
    @GET("api/customer/list")
    Call<ApiResponse<CustomerListResponse>> getCustomerList(
            @Query("page") int page,
            @Query("size") int size,
            @Query("keyword") String keyword);

    /**
     * 客户管理》获取客户详情
     *
     * @param id
     * @return
     */
    @GET("api/customer/detail")
    Call<ApiResponse<CustomerResponse>> getCustomerDetail(@Query("id") int id);

    /**
     * 客户管理》新增客户
     *
     * @param request
     * @return
     */
    @POST("api/customer/add")
    Call<ApiResponse<CustomerResponse>> createCustomer(@Body CustomerRequest request);

    /**
     * 客户管理》修改客户
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/customer/update")
    Call<ApiResponse<CustomerResponse>> updateCustomer(@Query("id") int id, @Body CustomerRequest request);

    /**
     * 客户管理》删除客户
     *
     * @param request
     * @return
     */
    @POST("api/customer/delete")
    Call<ApiResponse<Void>> deleteCustomer(@Body CustomerRequest request);

    /**
     * 通用图片上传接口
     *
     * @param businessType 业务类型 (customer, user, product等)
     * @param businessId   业务ID
     * @param operator     操作人
     * @param image        图片文件
     * @return String 图片URL或路径
     */
    @Multipart
    @POST("api/image/uploadImage")
    Call<ApiResponse<String>> uploadImage(@Part("businessType") RequestBody businessType,
                                          @Part("businessId") RequestBody businessId,
                                          @Part("operator") RequestBody operator,
                                          @Part MultipartBody.Part image);

    /**
     * 批量图片上传接口
     *
     * @param businessType 业务类型 (customer, user, product等)
     * @param businessId   业务ID
     * @param operator     操作人
     * @param images       图片文件列表
     * @return List<String> 图片URL或路径列表
     */
    @Multipart
    @POST("api/image/uploadMultipleImages")
    Call<ApiResponse<List<String>>> uploadMultipleImages(@Part("businessType") RequestBody businessType,
                                                         @Part("businessId") RequestBody businessId,
                                                         @Part("operator") RequestBody operator,
                                                         @Part List<MultipartBody.Part> images);

    /**
     * 删除图片接口
     *
     * @param businessType 业务类型 (customer, user, product等)
     * @param businessId   业务ID
     * @param operator     操作人
     * @param imageUrl     要删除的图片URL
     * @return 删除结果
     */
    @POST("api/image/deleteImage")
    Call<ApiResponse<Void>> deleteImage(@Query("businessType") String businessType,
                                        @Query("businessId") String businessId,
                                        @Query("operator") String operator,
                                        @Query("imageUrl") String imageUrl);

    /**
     * 获取业务对象的所有图片
     *
     * @param businessType 业务类型 (customer, user, product等)
     * @param businessId   业务ID
     * @return List<String> 图片URL列表
     */
    @GET("api/image/getImages")
    Call<ApiResponse<List<String>>> getImages(@Query("businessType") String businessType,
                                              @Query("businessId") String businessId);
    // 产品管理相关接口

    /**
     * 产品管理》获取产品列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 搜索关键词
     * @return 产品列表响应
     */
    @GET("api/product/list")
    Call<ApiResponse<ProductListResponse>> getProductList(@Query("page") int page,
                                                          @Query("size") int size,
                                                          @Query("keyword") String keyword);

    /**
     * 产品管理》获取产品详情
     *
     * @param id 产品ID
     * @return 产品详情响应
     */
    @GET("api/product/detail")
    Call<ApiResponse<ProductCompleteResponse>> getProductDetail(@Query("id") int id);

    /**
     * 产品管理》新增产品
     *
     * @param request 产品请求
     * @return 产品响应
     */
    @POST("api/product/add")
    Call<ApiResponse<ProductResponse>> createProduct(@Body ProductRequest request);

    /**
     * 产品管理》修改产品
     *
     * @param id      产品ID
     * @param request 产品请求
     * @return 产品响应
     */
    @POST("api/product/update")
    Call<ApiResponse<ProductResponse>> updateProduct(@Query("id") int id, @Body ProductRequest request);

    /**
     * 产品管理》删除产品
     *
     * @param request 产品请求
     * @return 删除响应
     */
    @POST("api/product/delete")
    Call<ApiResponse<Void>> deleteProduct(@Body ProductRequest request);

    // 订单相关接口
    @GET("api/orders/{id}")
    Call<ApiResponse<OrderResponse>> getOrder(@Path("id") int id);

    @POST("api/orders")
    Call<ApiResponse<OrderResponse>> createOrder(@Body OrderRequest request);

    @PUT("api/orders/{id}")
    Call<ApiResponse<OrderResponse>> updateOrder(@Path("id") int id, @Body OrderRequest request);

    @DELETE("api/orders/{id}")
    Call<ApiResponse<Void>> deleteOrder(@Path("id") int id);

    // 订单流程相关接口
    @GET("api/orders/{orderId}/processes")
    Call<ApiResponse<List<OrderResponse>>> getOrderProcesses(@Path("orderId") int orderId);

    @POST("api/orders/{orderId}/processes")
    Call<ApiResponse<OrderResponse>> createOrderProcess(@Path("orderId") int orderId, @Body OrderRequest request);

    @PUT("api/orders/{orderId}/processes/{processId}")
    Call<ApiResponse<OrderResponse>> updateOrderProcess(@Path("orderId") int orderId,
                                                        @Path("processId") int processId, @Body OrderRequest request);

    @DELETE("api/orders/{orderId}/processes/{processId}")
    Call<ApiResponse<Void>> deleteOrderProcess(@Path("orderId") int orderId, @Path("processId") int processId);

    /**
     * 流程岗位映射管理》获取流程岗位映射列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 搜索关键字
     * @return
     */
    @GET("api/processPostMapping/list")
    Call<ApiResponse<ProcessPostMappingListResponse>> getProcessPostMappings(
            @Query("page") int page,
            @Query("size") int size,
            @Query("keyword") String keyword);

    /**
     * 流程岗位映射管理》新增流程岗位映射
     *
     * @param request
     * @return
     */
    @POST("api/processPostMapping/add")
    Call<ApiResponse<ProcessPostMappingResponse>> createProcessPostMapping(@Body ProcessPostMappingRequest request);

    /**
     * 流程岗位映射管理》修改流程岗位映射
     *
     * @param id
     * @param request
     * @return
     */
    @POST("api/processPostMapping/update")
    Call<ApiResponse<ProcessPostMappingResponse>> updateProcessPostMapping(@Query("id") int id, @Body ProcessPostMappingRequest request);

    /**
     * 流程岗位映射管理》删除流程岗位映射
     *
     * @param request
     * @return
     */
    @POST("api/processPostMapping/delete")
    Call<ApiResponse<Void>> deleteProcessPostMapping(@Body ProcessPostMappingRequest request);

    /**
     * 部件管理》分页获取部件列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 搜索关键字
     * @return
     */
    @GET("api/component/list")
    Call<ApiResponse<ComponentListResponse>> getComponentList(
            @Query("page") int page,
            @Query("size") int size,
            @Query("keyword") String keyword);

    /**
     * 部件管理》获取部件详情
     *
     * @param id 部件ID
     * @return
     */
    @GET("api/component/detail")
    Call<ApiResponse<ComponentResponse>> getComponentDetail(@Query("id") int id);

    /**
     * 部件管理》新增部件
     *
     * @param request 部件请求
     * @return
     */
    @POST("api/component/add")
    Call<ApiResponse<ComponentResponse>> createComponent(@Body ComponentRequest request);

    /**
     * 部件管理》修改部件
     *
     * @param id      部件ID
     * @param request 部件请求
     * @return
     */
    @POST("api/component/update")
    Call<ApiResponse<ComponentResponse>> updateComponent(@Query("id") int id, @Body ComponentRequest request);

    /**
     * 部件管理》删除部件
     *
     * @param request 部件请求
     * @return
     */
    @POST("api/component/delete")
    Call<ApiResponse<Void>> deleteComponent(@Body ComponentRequest request);

}