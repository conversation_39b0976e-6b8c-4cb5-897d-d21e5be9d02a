<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_position_management" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_position_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_position_management_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="210" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/card_compact_toolbar" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="22" startOffset="4" endLine="83" endOffset="55"/></Target><Target id="@+id/btn_toggle_search" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="41" startOffset="12" endLine="50" endOffset="37"/></Target><Target id="@+id/tv_position_count" view="TextView"><Expressions/><location startLine="60" startOffset="12" endLine="67" endOffset="41"/></Target><Target id="@+id/btn_toggle_toolbar" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="70" startOffset="12" endLine="79" endOffset="37"/></Target><Target id="@+id/card_expandable_tools" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="86" startOffset="4" endLine="125" endOffset="55"/></Target><Target id="@+id/til_search" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="104" startOffset="12" endLine="121" endOffset="67"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="114" startOffset="16" endLine="119" endOffset="42"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="128" startOffset="4" endLine="147" endOffset="59"/></Target><Target id="@+id/rv_positions" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="136" startOffset="8" endLine="145" endOffset="52"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="150" startOffset="4" endLine="158" endOffset="51"/></Target><Target id="@+id/ll_empty" view="LinearLayout"><Expressions/><location startLine="161" startOffset="4" endLine="196" endOffset="18"/></Target><Target id="@+id/fab_add" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="199" startOffset="4" endLine="208" endOffset="33"/></Target></Targets></Layout>