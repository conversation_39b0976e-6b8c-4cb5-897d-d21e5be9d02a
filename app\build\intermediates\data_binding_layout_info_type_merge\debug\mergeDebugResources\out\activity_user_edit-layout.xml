<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_user_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/activity_user_edit_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="368" endOffset="39"/></Target><Target id="@+id/cardAvatar" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="15" startOffset="8" endLine="32" endOffset="43"/></Target><Target id="@+id/ivAvatar" view="ImageView"><Expressions/><location startLine="25" startOffset="12" endLine="31" endOffset="49"/></Target><Target id="@+id/tvUsername" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="46" endOffset="38"/></Target><Target id="@+id/cardBasicInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="49" startOffset="8" endLine="153" endOffset="59"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="74" startOffset="16" endLine="86" endOffset="71"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="81" startOffset="20" endLine="85" endOffset="50"/></Target><Target id="@+id/tilGender" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="89" startOffset="16" endLine="102" endOffset="71"/></Target><Target id="@+id/actGender" view="AutoCompleteTextView"><Expressions/><location startLine="97" startOffset="20" endLine="101" endOffset="50"/></Target><Target id="@+id/tilBirthday" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="105" startOffset="16" endLine="119" endOffset="71"/></Target><Target id="@+id/etBirthday" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="113" startOffset="20" endLine="118" endOffset="50"/></Target><Target id="@+id/tilIdCard" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="122" startOffset="16" endLine="135" endOffset="71"/></Target><Target id="@+id/etIdCard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="130" startOffset="20" endLine="134" endOffset="50"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="138" startOffset="16" endLine="151" endOffset="71"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="146" startOffset="20" endLine="150" endOffset="51"/></Target><Target id="@+id/cardWorkInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="156" startOffset="8" endLine="293" endOffset="59"/></Target><Target id="@+id/tilEmployeeId" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="181" startOffset="16" endLine="193" endOffset="71"/></Target><Target id="@+id/etEmployeeId" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="188" startOffset="20" endLine="192" endOffset="50"/></Target><Target id="@+id/tilRole" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="196" startOffset="16" endLine="209" endOffset="71"/></Target><Target id="@+id/actRole" view="AutoCompleteTextView"><Expressions/><location startLine="204" startOffset="20" endLine="208" endOffset="50"/></Target><Target id="@+id/tilDepartment" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="212" startOffset="16" endLine="225" endOffset="71"/></Target><Target id="@+id/actDepartment" view="AutoCompleteTextView"><Expressions/><location startLine="220" startOffset="20" endLine="224" endOffset="50"/></Target><Target id="@+id/tilPosition" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="228" startOffset="16" endLine="241" endOffset="71"/></Target><Target id="@+id/actPosition" view="AutoCompleteTextView"><Expressions/><location startLine="236" startOffset="20" endLine="240" endOffset="50"/></Target><Target id="@+id/tilJob" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="244" startOffset="16" endLine="257" endOffset="71"/></Target><Target id="@+id/actJob" view="AutoCompleteTextView"><Expressions/><location startLine="252" startOffset="20" endLine="256" endOffset="50"/></Target><Target id="@+id/tilPermissionTemplate" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="260" startOffset="16" endLine="273" endOffset="71"/></Target><Target id="@+id/actPermissionTemplate" view="AutoCompleteTextView"><Expressions/><location startLine="268" startOffset="20" endLine="272" endOffset="50"/></Target><Target id="@+id/tilRemark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="276" startOffset="16" endLine="291" endOffset="71"/></Target><Target id="@+id/etRemark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="284" startOffset="20" endLine="290" endOffset="43"/></Target><Target id="@+id/tvRegisterTimeLabel" view="TextView"><Expressions/><location startLine="296" startOffset="8" endLine="305" endOffset="68"/></Target><Target id="@+id/tvRegisterTime" view="TextView"><Expressions/><location startLine="307" startOffset="8" endLine="317" endOffset="46"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="320" startOffset="8" endLine="329" endOffset="70"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="331" startOffset="8" endLine="340" endOffset="60"/></Target><Target id="@+id/btnDeleteUser" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="343" startOffset="8" endLine="354" endOffset="44"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="357" startOffset="8" endLine="365" endOffset="55"/></Target></Targets></Layout>