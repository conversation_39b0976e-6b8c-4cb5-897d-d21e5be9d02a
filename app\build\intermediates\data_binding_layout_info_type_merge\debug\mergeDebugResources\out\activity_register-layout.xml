<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_register" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_register_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="140" endOffset="12"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="11" startOffset="8" endLine="19" endOffset="43"/></Target><Target id="@+id/til_username" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="21" startOffset="8" endLine="34" endOffset="63"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="28" startOffset="12" endLine="33" endOffset="41"/></Target><Target id="@+id/til_password" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="36" startOffset="8" endLine="49" endOffset="63"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="43" startOffset="12" endLine="48" endOffset="49"/></Target><Target id="@+id/til_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="51" startOffset="8" endLine="64" endOffset="63"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="58" startOffset="12" endLine="63" endOffset="41"/></Target><Target id="@+id/rg_gender" view="RadioGroup"><Expressions/><location startLine="66" startOffset="8" endLine="84" endOffset="20"/></Target><Target id="@+id/rb_male" view="RadioButton"><Expressions/><location startLine="73" startOffset="12" endLine="77" endOffset="33"/></Target><Target id="@+id/rb_female" view="RadioButton"><Expressions/><location startLine="79" startOffset="12" endLine="83" endOffset="33"/></Target><Target id="@+id/til_birthday" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="86" startOffset="8" endLine="100" endOffset="63"/></Target><Target id="@+id/et_birthday" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="93" startOffset="12" endLine="99" endOffset="41"/></Target><Target id="@+id/til_id_card" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="102" startOffset="8" endLine="115" endOffset="63"/></Target><Target id="@+id/et_id_card" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="109" startOffset="12" endLine="114" endOffset="41"/></Target><Target id="@+id/til_phone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="117" startOffset="8" endLine="130" endOffset="63"/></Target><Target id="@+id/et_phone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="124" startOffset="12" endLine="129" endOffset="42"/></Target><Target id="@+id/btn_register" view="Button"><Expressions/><location startLine="132" startOffset="8" endLine="137" endOffset="30"/></Target></Targets></Layout>