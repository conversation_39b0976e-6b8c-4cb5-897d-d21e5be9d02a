<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_component" modulePackage="com.opms" filePath="app\src\main\res\layout\item_component.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_component"><Targets><Target id="@+id/card_component" tag="layout/item_component_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="151" endOffset="51"/></Target><Target id="@+id/iv_component_image" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="28" endOffset="55"/></Target><Target id="@+id/tv_component_name" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="44" endOffset="72"/></Target><Target id="@+id/tv_component_code" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="60" endOffset="74"/></Target><Target id="@+id/tv_component_model" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="76" endOffset="74"/></Target><Target id="@+id/tv_component_standard" view="TextView"><Expressions/><location startLine="79" startOffset="8" endLine="92" endOffset="75"/></Target><Target id="@+id/tv_component_remark" view="TextView"><Expressions/><location startLine="95" startOffset="8" endLine="107" endOffset="75"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="120" startOffset="12" endLine="127" endOffset="46"/></Target><Target id="@+id/btn_delete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="129" startOffset="12" endLine="137" endOffset="45"/></Target><Target id="@+id/guideline" view="androidx.constraintlayout.widget.Guideline"><Expressions/><location startLine="142" startOffset="8" endLine="147" endOffset="54"/></Target></Targets></Layout>