<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_profile_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_profile_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/activity_profile_edit_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="155" endOffset="39"/></Target><Target id="@+id/cardPersonalInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="14" startOffset="8" endLine="117" endOffset="59"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="39" startOffset="16" endLine="51" endOffset="71"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="47" startOffset="20" endLine="50" endOffset="62"/></Target><Target id="@+id/tilGender" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="54" startOffset="16" endLine="67" endOffset="71"/></Target><Target id="@+id/actGender" view="AutoCompleteTextView"><Expressions/><location startLine="62" startOffset="20" endLine="66" endOffset="50"/></Target><Target id="@+id/tilBirthday" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="70" startOffset="16" endLine="84" endOffset="71"/></Target><Target id="@+id/etBirthday" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="78" startOffset="20" endLine="83" endOffset="50"/></Target><Target id="@+id/tilIdCard" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="87" startOffset="16" endLine="99" endOffset="71"/></Target><Target id="@+id/etIdCard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="95" startOffset="20" endLine="98" endOffset="62"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="102" startOffset="16" endLine="115" endOffset="71"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="110" startOffset="20" endLine="114" endOffset="51"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="121" startOffset="8" endLine="130" endOffset="72"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="132" startOffset="8" endLine="141" endOffset="60"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="144" startOffset="8" endLine="152" endOffset="55"/></Target></Targets></Layout>