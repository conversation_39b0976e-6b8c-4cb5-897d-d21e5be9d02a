<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_password_change" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_password_change.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_password_change_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="117" endOffset="51"/></Target><Target id="@+id/cardPasswordChange" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="8" startOffset="4" endLine="81" endOffset="55"/></Target><Target id="@+id/tilOldPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="32" startOffset="12" endLine="45" endOffset="67"/></Target><Target id="@+id/etOldPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="40" startOffset="16" endLine="44" endOffset="54"/></Target><Target id="@+id/tilNewPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="48" startOffset="12" endLine="62" endOffset="67"/></Target><Target id="@+id/etNewPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="16" endLine="61" endOffset="54"/></Target><Target id="@+id/tilConfirmPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="65" startOffset="12" endLine="79" endOffset="67"/></Target><Target id="@+id/etConfirmPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="74" startOffset="16" endLine="78" endOffset="54"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="84" startOffset="4" endLine="93" endOffset="70"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="95" startOffset="4" endLine="104" endOffset="56"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="107" startOffset="4" endLine="115" endOffset="51"/></Target></Targets></Layout>