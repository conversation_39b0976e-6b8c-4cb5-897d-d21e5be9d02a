<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_registration" modulePackage="com.opms" filePath="app\src\main\res\layout\item_registration.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_registration_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="49" endOffset="51"/></Target><Target id="@+id/tv_username" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="37"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="26" endOffset="43"/></Target><Target id="@+id/tv_id_card" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="32" endOffset="43"/></Target><Target id="@+id/tv_phone" view="TextView"><Expressions/><location startLine="34" startOffset="8" endLine="38" endOffset="43"/></Target><Target id="@+id/tv_register_time" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="45" endOffset="59"/></Target></Targets></Layout>