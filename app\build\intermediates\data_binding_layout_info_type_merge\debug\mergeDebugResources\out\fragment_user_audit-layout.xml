<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_user_audit" modulePackage="com.opms" filePath="app\src\main\res\layout\fragment_user_audit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_user_audit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="138" endOffset="51"/></Target><Target id="@+id/card_search" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="9" startOffset="4" endLine="86" endOffset="55"/></Target><Target id="@+id/til_search" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="25" startOffset="12" endLine="41" endOffset="67"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="34" startOffset="16" endLine="39" endOffset="42"/></Target><Target id="@+id/chip_group_status" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="50" startOffset="16" endLine="80" endOffset="60"/></Target><Target id="@+id/chip_pending" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="58" startOffset="20" endLine="64" endOffset="44"/></Target><Target id="@+id/chip_approved" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="66" startOffset="20" endLine="71" endOffset="44"/></Target><Target id="@+id/chip_rejected" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="73" startOffset="20" endLine="78" endOffset="44"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="89" startOffset="4" endLine="106" endOffset="59"/></Target><Target id="@+id/rv_users" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="97" startOffset="8" endLine="104" endOffset="56"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="109" startOffset="4" endLine="136" endOffset="18"/></Target></Targets></Layout>