<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_component_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_component_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_component_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="234" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/ll_image_container" view="LinearLayout"><Expressions/><location startLine="33" startOffset="12" endLine="78" endOffset="26"/></Target><Target id="@+id/card_image" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="44" startOffset="16" endLine="66" endOffset="67"/></Target><Target id="@+id/iv_component_image" view="ImageView"><Expressions/><location startLine="51" startOffset="20" endLine="56" endOffset="73"/></Target><Target id="@+id/view_image_overlay" view="View"><Expressions/><location startLine="58" startOffset="20" endLine="64" endOffset="50"/></Target><Target id="@+id/tv_image_hint" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="76" endOffset="47"/></Target><Target id="@+id/card_basic_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="81" startOffset="12" endLine="193" endOffset="47"/></Target><Target id="@+id/et_component_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="114" startOffset="24" endLine="119" endOffset="50"/></Target><Target id="@+id/et_component_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="131" startOffset="24" endLine="136" endOffset="50"/></Target><Target id="@+id/et_component_model" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="148" startOffset="24" endLine="153" endOffset="50"/></Target><Target id="@+id/et_component_standard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="165" startOffset="24" endLine="170" endOffset="50"/></Target><Target id="@+id/et_component_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="181" startOffset="24" endLine="187" endOffset="50"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="205" startOffset="16" endLine="211" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="213" startOffset="16" endLine="218" endOffset="50"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="227" startOffset="4" endLine="232" endOffset="35"/></Target></Targets></Layout>