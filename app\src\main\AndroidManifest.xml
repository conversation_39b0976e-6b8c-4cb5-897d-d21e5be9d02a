<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 存储权限 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name=".OPMSApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.OPMS"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="31">

        <!-- 启动页 -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.OPMS.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录页 -->
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 注册页 -->
        <activity
            android:name=".ui.register.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 主页面 -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 文件提供者 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 这些功能尚未实现，暂时注释掉
        <activity
            android:name=".ui.UserAuditActivity"
            android:exported="false"
            android:label="用户审核" />

        <activity
            android:name=".ui.RegistrationDetailActivity"
            android:exported="false"
            android:label="注册申请详情" />
        -->

        <!-- 用户资料页面 -->
        <activity
            android:name=".ui.profile.ProfileActivity"
            android:exported="false" />

        <!-- 头像查看/编辑页面 -->
        <activity
            android:name=".ui.profile.AvatarViewActivity"
            android:exported="false"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 个人信息编辑页面 -->
        <activity
            android:name=".ui.profile.ProfileEditActivity"
            android:exported="false"
            android:label="编辑个人信息" />

        <!-- 修改密码页面 -->
        <activity
            android:name=".ui.profile.PasswordChangeActivity"
            android:exported="false"
            android:label="修改密码" />

        <!-- 用户管理页面 -->
        <activity
            android:name=".ui.system.UserManagementActivity"
            android:exported="false"
            android:label="用户管理" />

        <!-- 用户编辑页面 -->
        <activity
            android:name=".ui.system.UserEditActivity"
            android:exported="false"
            android:label="编辑用户信息" />

        <!-- 部门管理页面 -->
        <activity
            android:name=".ui.system.DepartmentManagementActivity"
            android:exported="false"
            android:label="部门管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 职位管理页面 -->
        <activity
            android:name=".ui.system.PositionManagementActivity"
            android:exported="false"
            android:label="职位管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 职位编辑页面 -->
        <activity
            android:name=".ui.system.PositionEditActivity"
            android:exported="false"
            android:label="职位编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 岗位管理页面 -->
        <activity
            android:name=".ui.system.PostManagementActivity"
            android:exported="false"
            android:label="岗位管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 岗位编辑页面 -->
        <activity
            android:name=".ui.system.PostEditActivity"
            android:exported="false"
            android:label="岗位编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 流程管理页面 -->
        <activity
            android:name=".ui.system.ProcessTemplateManagementActivity"
            android:exported="false"
            android:label="流程管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 流程编辑页面 -->
        <activity
            android:name=".ui.system.ProcessTemplateEditActivity"
            android:exported="false"
            android:label="流程编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 权限管理页面 -->
        <activity
            android:name=".ui.system.PermissionManagementActivity"
            android:exported="false"
            android:label="权限管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 权限编辑页面 -->
        <activity
            android:name=".ui.system.PermissionEditActivity"
            android:exported="false"
            android:label="权限编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 流程岗位映射管理页面 -->
        <activity
            android:name=".ui.system.ProcessPostMappingManagementActivity"
            android:exported="false"
            android:label="流程岗位映射"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 流程岗位映射编辑页面 -->
        <activity
            android:name=".ui.system.ProcessPostMappingEditActivity"
            android:exported="false"
            android:label="流程岗位映射编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 用户审核列表页面 -->
        <activity
            android:name=".ui.system.UserAuditListActivity"
            android:exported="false"
            android:label="用户审核"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 用户审核详情页面 -->
        <activity
            android:name=".ui.system.UserAuditActivity"
            android:exported="false"
            android:label="用户审核"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 客户管理页面 -->
        <activity
            android:name=".ui.business.CustomerManagementActivity"
            android:exported="false"
            android:label="客户管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 客户编辑页面 -->
        <activity
            android:name=".ui.business.CustomerEditActivity"
            android:exported="false"
            android:label="客户编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 产品管理页面 -->
        <activity
            android:name=".ui.business.ProductManagementActivity"
            android:exported="false"
            android:label="产品管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 产品编辑页面 -->
        <activity
            android:name=".ui.business.ProductEditActivity"
            android:exported="false"
            android:label="产品编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 订单管理页面 -->
        <activity
            android:name=".ui.business.OrderManagementActivity"
            android:exported="false"
            android:label="订单管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 部件管理页面 -->
        <activity
            android:name=".ui.business.ComponentManagementActivity"
            android:exported="false"
            android:label="部件管理"
            android:theme="@style/Theme.OPMS.NoActionBar" />

        <!-- 部件编辑页面 -->
        <activity
            android:name=".ui.business.ComponentEditActivity"
            android:exported="false"
            android:label="部件编辑"
            android:theme="@style/Theme.OPMS.NoActionBar" />

    </application>

</manifest>