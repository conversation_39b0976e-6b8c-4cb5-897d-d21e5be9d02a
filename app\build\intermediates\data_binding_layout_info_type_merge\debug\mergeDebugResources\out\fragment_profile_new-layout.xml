<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile_new" modulePackage="com.opms" filePath="app\src\main\res\layout\fragment_profile_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_profile_new_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="355" endOffset="39"/></Target><Target id="@+id/cardAvatar" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="13" startOffset="8" endLine="29" endOffset="43"/></Target><Target id="@+id/ivAvatar" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="28" endOffset="48"/></Target><Target id="@+id/tvDisplayUsername" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="42" endOffset="66"/></Target><Target id="@+id/cardPersonalInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="8" endLine="157" endOffset="59"/></Target><Target id="@+id/tilUsername" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="69" startOffset="16" endLine="81" endOffset="71"/></Target><Target id="@+id/etUsername" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="20" endLine="80" endOffset="49"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="83" startOffset="16" endLine="95" endOffset="71"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="91" startOffset="20" endLine="94" endOffset="62"/></Target><Target id="@+id/tilGender" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="97" startOffset="16" endLine="110" endOffset="71"/></Target><Target id="@+id/actGender" view="AutoCompleteTextView"><Expressions/><location startLine="105" startOffset="20" endLine="109" endOffset="50"/></Target><Target id="@+id/tilBirthday" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="112" startOffset="16" endLine="126" endOffset="71"/></Target><Target id="@+id/etBirthday" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="120" startOffset="20" endLine="125" endOffset="50"/></Target><Target id="@+id/tilIdCard" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="128" startOffset="16" endLine="140" endOffset="71"/></Target><Target id="@+id/etIdCard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="136" startOffset="20" endLine="139" endOffset="62"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="142" startOffset="16" endLine="155" endOffset="71"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="150" startOffset="20" endLine="154" endOffset="51"/></Target><Target id="@+id/cardWorkInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="160" startOffset="8" endLine="272" endOffset="59"/></Target><Target id="@+id/tilEmployeeId" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="184" startOffset="16" endLine="195" endOffset="71"/></Target><Target id="@+id/etEmployeeId" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="191" startOffset="20" endLine="194" endOffset="62"/></Target><Target id="@+id/tilRole" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="197" startOffset="16" endLine="210" endOffset="71"/></Target><Target id="@+id/actRole" view="AutoCompleteTextView"><Expressions/><location startLine="205" startOffset="20" endLine="209" endOffset="50"/></Target><Target id="@+id/tilDepartment" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="212" startOffset="16" endLine="225" endOffset="71"/></Target><Target id="@+id/actDepartment" view="AutoCompleteTextView"><Expressions/><location startLine="220" startOffset="20" endLine="224" endOffset="50"/></Target><Target id="@+id/tilPosition" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="227" startOffset="16" endLine="240" endOffset="71"/></Target><Target id="@+id/actPosition" view="AutoCompleteTextView"><Expressions/><location startLine="235" startOffset="20" endLine="239" endOffset="50"/></Target><Target id="@+id/tilJob" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="242" startOffset="16" endLine="255" endOffset="71"/></Target><Target id="@+id/actJob" view="AutoCompleteTextView"><Expressions/><location startLine="250" startOffset="20" endLine="254" endOffset="50"/></Target><Target id="@+id/tilPermissionTemplate" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="257" startOffset="16" endLine="270" endOffset="71"/></Target><Target id="@+id/actPermissionTemplate" view="AutoCompleteTextView"><Expressions/><location startLine="265" startOffset="20" endLine="269" endOffset="50"/></Target><Target id="@+id/cardOtherInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="275" startOffset="8" endLine="330" endOffset="59"/></Target><Target id="@+id/tilRemark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="299" startOffset="16" endLine="313" endOffset="71"/></Target><Target id="@+id/etRemark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="306" startOffset="20" endLine="312" endOffset="43"/></Target><Target id="@+id/tilRegisterTime" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="315" startOffset="16" endLine="328" endOffset="71"/></Target><Target id="@+id/etRegisterTime" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="323" startOffset="20" endLine="327" endOffset="49"/></Target><Target id="@+id/btnEdit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="333" startOffset="8" endLine="342" endOffset="69"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="344" startOffset="8" endLine="352" endOffset="60"/></Target></Targets></Layout>