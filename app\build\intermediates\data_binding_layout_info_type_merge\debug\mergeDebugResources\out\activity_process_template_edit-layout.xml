<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_process_template_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_process_template_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_process_template_edit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="202" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/til_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="60" startOffset="20" endLine="78" endOffset="75"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="71" startOffset="24" endLine="76" endOffset="52"/></Target><Target id="@+id/til_code" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="81" startOffset="20" endLine="100" endOffset="75"/></Target><Target id="@+id/et_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="93" startOffset="24" endLine="98" endOffset="52"/></Target><Target id="@+id/til_sequence" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="103" startOffset="20" endLine="119" endOffset="75"/></Target><Target id="@+id/et_sequence" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="112" startOffset="24" endLine="117" endOffset="51"/></Target><Target id="@+id/til_description" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="122" startOffset="20" endLine="142" endOffset="75"/></Target><Target id="@+id/et_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="133" startOffset="24" endLine="140" endOffset="50"/></Target><Target id="@+id/switch_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="159" startOffset="24" endLine="163" endOffset="52"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="178" startOffset="16" endLine="185" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="188" startOffset="16" endLine="194" endOffset="39"/></Target></Targets></Layout>