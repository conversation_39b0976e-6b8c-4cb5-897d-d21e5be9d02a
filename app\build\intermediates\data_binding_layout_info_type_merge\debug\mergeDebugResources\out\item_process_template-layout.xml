<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_process_template" modulePackage="com.opms" filePath="app\src\main\res\layout\item_process_template.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_process_template_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="254" endOffset="51"/></Target><Target id="@+id/ll_view_mode" view="LinearLayout"><Expressions/><location startLine="39" startOffset="12" endLine="120" endOffset="26"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="52" startOffset="20" endLine="60" endOffset="43"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="72" endOffset="58"/></Target><Target id="@+id/tv_code" view="TextView"><Expressions/><location startLine="83" startOffset="20" endLine="90" endOffset="46"/></Target><Target id="@+id/tv_sequence" view="TextView"><Expressions/><location startLine="92" startOffset="20" endLine="103" endOffset="44"/></Target><Target id="@+id/tv_description" view="TextView"><Expressions/><location startLine="108" startOffset="16" endLine="117" endOffset="63"/></Target><Target id="@+id/ll_edit_mode" view="LinearLayout"><Expressions/><location startLine="123" startOffset="12" endLine="234" endOffset="26"/></Target><Target id="@+id/til_edit_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="131" startOffset="16" endLine="146" endOffset="71"/></Target><Target id="@+id/et_edit_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="139" startOffset="20" endLine="144" endOffset="48"/></Target><Target id="@+id/til_edit_description" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="149" startOffset="16" endLine="165" endOffset="71"/></Target><Target id="@+id/et_edit_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="157" startOffset="20" endLine="163" endOffset="46"/></Target><Target id="@+id/til_edit_sequence" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="168" startOffset="16" endLine="183" endOffset="71"/></Target><Target id="@+id/et_edit_sequence" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="176" startOffset="20" endLine="181" endOffset="47"/></Target><Target id="@+id/switch_edit_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="199" startOffset="20" endLine="204" endOffset="57"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="212" startOffset="20" endLine="220" endOffset="49"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="223" startOffset="20" endLine="230" endOffset="49"/></Target><Target id="@+id/iv_delete" view="ImageView"><Expressions/><location startLine="239" startOffset="8" endLine="250" endOffset="37"/></Target></Targets></Layout>