<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_permission_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_permission_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_permission_edit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="249" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/progress_loading" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="22" startOffset="4" endLine="28" endOffset="59"/></Target><Target id="@+id/scroll_content" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="31" startOffset="4" endLine="247" endOffset="43"/></Target><Target id="@+id/til_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="70" startOffset="20" endLine="88" endOffset="75"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="81" startOffset="24" endLine="86" endOffset="52"/></Target><Target id="@+id/til_code" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="91" startOffset="20" endLine="110" endOffset="75"/></Target><Target id="@+id/et_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="103" startOffset="24" endLine="108" endOffset="52"/></Target><Target id="@+id/til_description" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="113" startOffset="20" endLine="133" endOffset="75"/></Target><Target id="@+id/et_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="124" startOffset="24" endLine="131" endOffset="50"/></Target><Target id="@+id/switch_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="150" startOffset="24" endLine="154" endOffset="52"/></Target><Target id="@+id/btn_select_all" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="194" startOffset="24" endLine="201" endOffset="53"/></Target><Target id="@+id/rv_business_modules" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="206" startOffset="20" endLine="212" endOffset="80"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="225" startOffset="16" endLine="232" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="235" startOffset="16" endLine="241" endOffset="39"/></Target></Targets></Layout>