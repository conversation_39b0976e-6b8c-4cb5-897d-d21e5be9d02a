<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_registration_detail" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_registration_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_registration_detail_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="201" endOffset="12"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="12" startOffset="8" endLine="19" endOffset="43"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="27" startOffset="12" endLine="32" endOffset="40"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="40"/></Target><Target id="@+id/et_id_card" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="55" startOffset="12" endLine="60" endOffset="40"/></Target><Target id="@+id/et_phone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="12" endLine="74" endOffset="40"/></Target><Target id="@+id/et_employee_id" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="83" startOffset="12" endLine="87" endOffset="34"/></Target><Target id="@+id/act_role_type" view="AutoCompleteTextView"><Expressions/><location startLine="96" startOffset="12" endLine="101" endOffset="41"/></Target><Target id="@+id/act_department" view="AutoCompleteTextView"><Expressions/><location startLine="110" startOffset="12" endLine="115" endOffset="41"/></Target><Target id="@+id/act_position" view="AutoCompleteTextView"><Expressions/><location startLine="124" startOffset="12" endLine="129" endOffset="41"/></Target><Target id="@+id/act_job" view="AutoCompleteTextView"><Expressions/><location startLine="138" startOffset="12" endLine="143" endOffset="41"/></Target><Target id="@+id/et_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="152" startOffset="12" endLine="158" endOffset="37"/></Target><Target id="@+id/et_audit_comment" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="167" startOffset="12" endLine="173" endOffset="37"/></Target><Target id="@+id/btn_approve" view="Button"><Expressions/><location startLine="182" startOffset="12" endLine="188" endOffset="36"/></Target><Target id="@+id/btn_reject" view="Button"><Expressions/><location startLine="190" startOffset="12" endLine="196" endOffset="37"/></Target></Targets></Layout>