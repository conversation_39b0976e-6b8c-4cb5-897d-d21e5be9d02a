<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_audit_list" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_user_audit_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_user_audit_list_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="152" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/card_search" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="22" startOffset="4" endLine="99" endOffset="55"/></Target><Target id="@+id/til_search" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="38" startOffset="12" endLine="54" endOffset="67"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="47" startOffset="16" endLine="52" endOffset="42"/></Target><Target id="@+id/chip_group_status" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="63" startOffset="16" endLine="93" endOffset="60"/></Target><Target id="@+id/chip_pending" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="71" startOffset="20" endLine="77" endOffset="44"/></Target><Target id="@+id/chip_approved" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="79" startOffset="20" endLine="84" endOffset="44"/></Target><Target id="@+id/chip_rejected" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="86" startOffset="20" endLine="91" endOffset="44"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="102" startOffset="4" endLine="119" endOffset="59"/></Target><Target id="@+id/rv_users" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="110" startOffset="8" endLine="117" endOffset="56"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="122" startOffset="4" endLine="150" endOffset="18"/></Target><Target id="@+id/tv_empty_message" view="TextView"><Expressions/><location startLine="141" startOffset="8" endLine="148" endOffset="37"/></Target></Targets></Layout>