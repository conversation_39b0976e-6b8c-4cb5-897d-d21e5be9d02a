<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_product_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="339" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/card_images" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="33" startOffset="12" endLine="66" endOffset="47"/></Target><Target id="@+id/rv_product_images" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="58" startOffset="20" endLine="62" endOffset="64"/></Target><Target id="@+id/card_basic_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="69" startOffset="12" endLine="215" endOffset="47"/></Target><Target id="@+id/et_product_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="102" startOffset="24" endLine="107" endOffset="50"/></Target><Target id="@+id/et_product_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="119" startOffset="24" endLine="124" endOffset="50"/></Target><Target id="@+id/et_product_model" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="136" startOffset="24" endLine="141" endOffset="50"/></Target><Target id="@+id/et_product_standard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="153" startOffset="24" endLine="158" endOffset="50"/></Target><Target id="@+id/et_product_price" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="170" startOffset="24" endLine="175" endOffset="50"/></Target><Target id="@+id/spinner_status" view="AutoCompleteTextView"><Expressions/><location startLine="187" startOffset="24" endLine="192" endOffset="47"/></Target><Target id="@+id/et_product_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="203" startOffset="24" endLine="209" endOffset="50"/></Target><Target id="@+id/card_component_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="218" startOffset="12" endLine="298" endOffset="47"/></Target><Target id="@+id/btn_add_component" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="251" startOffset="24" endLine="257" endOffset="57"/></Target><Target id="@+id/ll_component_list" view="LinearLayout"><Expressions/><location startLine="262" startOffset="20" endLine="267" endOffset="56"/></Target><Target id="@+id/ll_component_empty" view="LinearLayout"><Expressions/><location startLine="270" startOffset="20" endLine="294" endOffset="34"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="310" startOffset="16" endLine="316" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="318" startOffset="16" endLine="323" endOffset="50"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="332" startOffset="4" endLine="337" endOffset="35"/></Target></Targets></Layout>