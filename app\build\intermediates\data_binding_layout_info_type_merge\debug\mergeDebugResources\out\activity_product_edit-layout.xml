<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_product_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="351" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/ll_image_container" view="LinearLayout"><Expressions/><location startLine="33" startOffset="12" endLine="78" endOffset="26"/></Target><Target id="@+id/card_image" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="44" startOffset="16" endLine="66" endOffset="67"/></Target><Target id="@+id/iv_product_image" view="ImageView"><Expressions/><location startLine="51" startOffset="20" endLine="56" endOffset="71"/></Target><Target id="@+id/view_image_overlay" view="View"><Expressions/><location startLine="58" startOffset="20" endLine="64" endOffset="50"/></Target><Target id="@+id/tv_image_hint" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="76" endOffset="47"/></Target><Target id="@+id/card_basic_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="81" startOffset="12" endLine="227" endOffset="47"/></Target><Target id="@+id/et_product_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="114" startOffset="24" endLine="119" endOffset="50"/></Target><Target id="@+id/et_product_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="131" startOffset="24" endLine="136" endOffset="50"/></Target><Target id="@+id/et_product_model" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="148" startOffset="24" endLine="153" endOffset="50"/></Target><Target id="@+id/et_product_standard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="165" startOffset="24" endLine="170" endOffset="50"/></Target><Target id="@+id/et_product_price" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="182" startOffset="24" endLine="187" endOffset="50"/></Target><Target id="@+id/spinner_status" view="AutoCompleteTextView"><Expressions/><location startLine="199" startOffset="24" endLine="204" endOffset="47"/></Target><Target id="@+id/et_product_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="215" startOffset="24" endLine="221" endOffset="50"/></Target><Target id="@+id/card_component_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="230" startOffset="12" endLine="310" endOffset="47"/></Target><Target id="@+id/btn_add_component" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="263" startOffset="24" endLine="269" endOffset="57"/></Target><Target id="@+id/ll_component_list" view="LinearLayout"><Expressions/><location startLine="274" startOffset="20" endLine="279" endOffset="56"/></Target><Target id="@+id/ll_component_empty" view="LinearLayout"><Expressions/><location startLine="282" startOffset="20" endLine="306" endOffset="34"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="322" startOffset="16" endLine="328" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="330" startOffset="16" endLine="335" endOffset="50"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="344" startOffset="4" endLine="349" endOffset="35"/></Target></Targets></Layout>