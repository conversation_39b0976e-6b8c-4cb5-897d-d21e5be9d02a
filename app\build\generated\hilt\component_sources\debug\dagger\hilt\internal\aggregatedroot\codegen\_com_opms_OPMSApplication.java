package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.opms.OPMSApplication",
    rootPackage = "com.opms",
    originatingRoot = "com.opms.OPMSApplication",
    originatingRootPackage = "com.opms",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "OPMSApplication",
    originatingRootSimpleNames = "OPMSApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_opms_OPMSApplication {
}
