<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product_component" modulePackage="com.opms" filePath="app\src\main\res\layout\item_product_component.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_component"><Targets><Target id="@+id/card_component" tag="layout/item_product_component_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="202" endOffset="51"/></Target><Target id="@+id/iv_component_image" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="31" endOffset="65"/></Target><Target id="@+id/tv_component_name" view="TextView"><Expressions/><location startLine="49" startOffset="16" endLine="57" endOffset="46"/></Target><Target id="@+id/tv_component_status" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="69" endOffset="45"/></Target><Target id="@+id/tv_component_code" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="81" endOffset="41"/></Target><Target id="@+id/tv_component_model" view="TextView"><Expressions/><location startLine="90" startOffset="16" endLine="97" endOffset="45"/></Target><Target id="@+id/tv_component_standard" view="TextView"><Expressions/><location startLine="99" startOffset="16" endLine="106" endOffset="45"/></Target><Target id="@+id/tv_component_quantity" view="TextView"><Expressions/><location startLine="134" startOffset="20" endLine="141" endOffset="50"/></Target><Target id="@+id/til_component_quantity" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="144" startOffset="20" endLine="165" endOffset="75"/></Target><Target id="@+id/et_component_quantity" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="156" startOffset="24" endLine="163" endOffset="53"/></Target><Target id="@+id/btn_edit_component" view="ImageView"><Expressions/><location startLine="170" startOffset="16" endLine="181" endOffset="52"/></Target><Target id="@+id/btn_delete_component" view="ImageView"><Expressions/><location startLine="184" startOffset="16" endLine="194" endOffset="50"/></Target></Targets></Layout>