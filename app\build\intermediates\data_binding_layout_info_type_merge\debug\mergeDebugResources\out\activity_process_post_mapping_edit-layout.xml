<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_process_post_mapping_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_process_post_mapping_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_process_post_mapping_edit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="182" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="28"/></Target><Target id="@+id/spinner_process" view="Spinner"><Expressions/><location startLine="43" startOffset="12" endLine="53" endOffset="46"/></Target><Target id="@+id/spinner_post" view="Spinner"><Expressions/><location startLine="65" startOffset="12" endLine="75" endOffset="46"/></Target><Target id="@+id/til_remark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="78" startOffset="12" endLine="96" endOffset="67"/></Target><Target id="@+id/et_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="87" startOffset="16" endLine="94" endOffset="51"/></Target><Target id="@+id/switch_status" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="137" startOffset="20" endLine="141" endOffset="48"/></Target><Target id="@+id/ll_buttons" view="LinearLayout"><Expressions/><location startLine="152" startOffset="4" endLine="180" endOffset="18"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="163" startOffset="8" endLine="170" endOffset="31"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="172" startOffset="8" endLine="178" endOffset="31"/></Target></Targets></Layout>