<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile_compact" modulePackage="com.opms" filePath="app\src\main\res\layout\fragment_profile_compact.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_profile_compact_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="357" endOffset="39"/></Target><Target id="@+id/cardAvatar" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="13" startOffset="8" endLine="29" endOffset="43"/></Target><Target id="@+id/ivAvatar" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="28" endOffset="48"/></Target><Target id="@+id/tvDisplayUsername" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="42" endOffset="66"/></Target><Target id="@+id/cardPersonalInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="8" endLine="144" endOffset="59"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="70" startOffset="16" endLine="82" endOffset="71"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="78" startOffset="20" endLine="81" endOffset="62"/></Target><Target id="@+id/tilGender" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="84" startOffset="16" endLine="97" endOffset="71"/></Target><Target id="@+id/actGender" view="AutoCompleteTextView"><Expressions/><location startLine="92" startOffset="20" endLine="96" endOffset="50"/></Target><Target id="@+id/tilBirthday" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="99" startOffset="16" endLine="113" endOffset="71"/></Target><Target id="@+id/etBirthday" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="107" startOffset="20" endLine="112" endOffset="50"/></Target><Target id="@+id/tilIdCard" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="115" startOffset="16" endLine="127" endOffset="71"/></Target><Target id="@+id/etIdCard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="123" startOffset="20" endLine="126" endOffset="62"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="129" startOffset="16" endLine="142" endOffset="71"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="137" startOffset="20" endLine="141" endOffset="51"/></Target><Target id="@+id/cardWorkInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="147" startOffset="8" endLine="259" endOffset="59"/></Target><Target id="@+id/tilEmployeeId" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="171" startOffset="16" endLine="182" endOffset="71"/></Target><Target id="@+id/etEmployeeId" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="178" startOffset="20" endLine="181" endOffset="62"/></Target><Target id="@+id/tilRole" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="184" startOffset="16" endLine="197" endOffset="71"/></Target><Target id="@+id/actRole" view="AutoCompleteTextView"><Expressions/><location startLine="192" startOffset="20" endLine="196" endOffset="50"/></Target><Target id="@+id/tilDepartment" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="199" startOffset="16" endLine="212" endOffset="71"/></Target><Target id="@+id/actDepartment" view="AutoCompleteTextView"><Expressions/><location startLine="207" startOffset="20" endLine="211" endOffset="50"/></Target><Target id="@+id/tilPosition" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="214" startOffset="16" endLine="227" endOffset="71"/></Target><Target id="@+id/actPosition" view="AutoCompleteTextView"><Expressions/><location startLine="222" startOffset="20" endLine="226" endOffset="50"/></Target><Target id="@+id/tilJob" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="229" startOffset="16" endLine="242" endOffset="71"/></Target><Target id="@+id/actJob" view="AutoCompleteTextView"><Expressions/><location startLine="237" startOffset="20" endLine="241" endOffset="50"/></Target><Target id="@+id/tilPermissionTemplate" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="244" startOffset="16" endLine="257" endOffset="71"/></Target><Target id="@+id/actPermissionTemplate" view="AutoCompleteTextView"><Expressions/><location startLine="252" startOffset="20" endLine="256" endOffset="50"/></Target><Target id="@+id/cardOtherInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="262" startOffset="8" endLine="317" endOffset="59"/></Target><Target id="@+id/tilRemark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="286" startOffset="16" endLine="300" endOffset="71"/></Target><Target id="@+id/etRemark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="293" startOffset="20" endLine="299" endOffset="43"/></Target><Target id="@+id/tilRegisterTime" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="302" startOffset="16" endLine="315" endOffset="71"/></Target><Target id="@+id/etRegisterTime" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="310" startOffset="20" endLine="314" endOffset="49"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="320" startOffset="8" endLine="328" endOffset="69"/></Target><Target id="@+id/btnEdit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="331" startOffset="8" endLine="341" endOffset="65"/></Target><Target id="@+id/btnChangePassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="344" startOffset="8" endLine="354" endOffset="65"/></Target></Targets></Layout>