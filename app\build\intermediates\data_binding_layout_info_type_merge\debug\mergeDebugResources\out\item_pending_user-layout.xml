<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_pending_user" modulePackage="com.opms" filePath="app\src\main\res\layout\item_pending_user.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_pending_user_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="51"/></Target><Target id="@+id/iv_avatar" view="ImageView"><Expressions/><location startLine="25" startOffset="12" endLine="31" endOffset="50"/></Target><Target id="@+id/tv_username" view="TextView"><Expressions/><location startLine="49" startOffset="16" endLine="56" endOffset="42"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="58" startOffset="16" endLine="65" endOffset="37"/></Target><Target id="@+id/tv_gender" view="TextView"><Expressions/><location startLine="76" startOffset="16" endLine="85" endOffset="36"/></Target><Target id="@+id/tv_phone" view="TextView"><Expressions/><location startLine="87" startOffset="16" endLine="94" endOffset="46"/></Target><Target id="@+id/tv_register_time" view="TextView"><Expressions/><location startLine="99" startOffset="12" endLine="106" endOffset="52"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="118" startOffset="12" endLine="128" endOffset="34"/></Target><Target id="@+id/btn_audit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="131" startOffset="12" endLine="139" endOffset="41"/></Target></Targets></Layout>