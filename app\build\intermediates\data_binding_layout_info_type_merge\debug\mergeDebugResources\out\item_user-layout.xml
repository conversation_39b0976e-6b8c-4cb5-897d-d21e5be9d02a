<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_user" modulePackage="com.opms" filePath="app\src\main\res\layout\item_user.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_user_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="185" endOffset="51"/></Target><Target id="@+id/ivAvatar" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="45"/></Target><Target id="@+id/tvUsername" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/tvRole" view="TextView"><Expressions/><location startLine="44" startOffset="8" endLine="58" endOffset="30"/></Target><Target id="@+id/tvName" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="76" endOffset="29"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="79" startOffset="8" endLine="85" endOffset="62"/></Target><Target id="@+id/tvEmployeeId" view="TextView"><Expressions/><location startLine="108" startOffset="16" endLine="117" endOffset="41"/></Target><Target id="@+id/tvPhone" view="TextView"><Expressions/><location startLine="127" startOffset="16" endLine="136" endOffset="46"/></Target><Target id="@+id/tvDepartment" view="TextView"><Expressions/><location startLine="153" startOffset="16" endLine="162" endOffset="47"/></Target><Target id="@+id/tvPosition" view="TextView"><Expressions/><location startLine="172" startOffset="16" endLine="181" endOffset="46"/></Target></Targets></Layout>