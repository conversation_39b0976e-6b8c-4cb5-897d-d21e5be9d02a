<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customer_management" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_customer_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_customer_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="190" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="6" startOffset="4" endLine="19" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="11" startOffset="8" endLine="17" endOffset="66"/></Target><Target id="@+id/btn_toggle_toolbar" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="27" startOffset="8" endLine="36" endOffset="55"/></Target><Target id="@+id/tv_customer_count" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="48" endOffset="55"/></Target><Target id="@+id/card_expandable_tools" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="51" startOffset="8" endLine="98" endOffset="59"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="77" startOffset="20" endLine="82" endOffset="46"/></Target><Target id="@+id/btn_clear_search" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="87" startOffset="16" endLine="94" endOffset="41"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="101" startOffset="8" endLine="176" endOffset="63"/></Target><Target id="@+id/rv_customers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="115" startOffset="16" endLine="124" endOffset="63"/></Target><Target id="@+id/ll_empty" view="LinearLayout"><Expressions/><location startLine="127" startOffset="16" endLine="161" endOffset="30"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="164" startOffset="16" endLine="172" endOffset="63"/></Target><Target id="@+id/fab_add" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="181" startOffset="4" endLine="188" endOffset="42"/></Target></Targets></Layout>