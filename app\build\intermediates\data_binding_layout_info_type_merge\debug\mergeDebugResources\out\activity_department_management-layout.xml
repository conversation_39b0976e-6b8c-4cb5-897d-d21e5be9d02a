<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_department_management" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_department_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_department_management_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="351" endOffset="51"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/card_compact_toolbar" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="22" startOffset="4" endLine="83" endOffset="55"/></Target><Target id="@+id/btn_toggle_search" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="41" startOffset="12" endLine="50" endOffset="37"/></Target><Target id="@+id/tv_department_count" view="TextView"><Expressions/><location startLine="60" startOffset="12" endLine="67" endOffset="41"/></Target><Target id="@+id/btn_toggle_toolbar" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="70" startOffset="12" endLine="79" endOffset="37"/></Target><Target id="@+id/card_expandable_tools" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="86" startOffset="4" endLine="160" endOffset="55"/></Target><Target id="@+id/til_search" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="104" startOffset="12" endLine="121" endOffset="67"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="114" startOffset="16" endLine="119" endOffset="42"/></Target><Target id="@+id/btn_expand_all" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="132" startOffset="16" endLine="142" endOffset="41"/></Target><Target id="@+id/btn_collapse_all" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="144" startOffset="16" endLine="154" endOffset="41"/></Target><Target id="@+id/card_selected_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="163" startOffset="4" endLine="266" endOffset="55"/></Target><Target id="@+id/tv_selected_department" view="TextView"><Expressions/><location startLine="194" startOffset="16" endLine="203" endOffset="46"/></Target><Target id="@+id/btn_clear_selection" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="205" startOffset="16" endLine="213" endOffset="41"/></Target><Target id="@+id/btn_add_child" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="224" startOffset="16" endLine="234" endOffset="41"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="236" startOffset="16" endLine="246" endOffset="41"/></Target><Target id="@+id/btn_delete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="248" startOffset="16" endLine="260" endOffset="52"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="269" startOffset="4" endLine="288" endOffset="59"/></Target><Target id="@+id/rv_departments" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="277" startOffset="8" endLine="286" endOffset="59"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="291" startOffset="4" endLine="299" endOffset="51"/></Target><Target id="@+id/ll_empty" view="LinearLayout"><Expressions/><location startLine="302" startOffset="4" endLine="337" endOffset="18"/></Target><Target id="@+id/fab_add_root" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="340" startOffset="4" endLine="349" endOffset="33"/></Target></Targets></Layout>