<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_component_management" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_component_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_component_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="192" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="21" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="66"/></Target><Target id="@+id/btn_toggle_toolbar" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="29" startOffset="8" endLine="38" endOffset="55"/></Target><Target id="@+id/tv_component_count" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="50" endOffset="55"/></Target><Target id="@+id/card_expandable_tools" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="53" startOffset="8" endLine="100" endOffset="59"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="79" startOffset="20" endLine="84" endOffset="46"/></Target><Target id="@+id/btn_clear_search" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="89" startOffset="16" endLine="96" endOffset="41"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="103" startOffset="8" endLine="178" endOffset="63"/></Target><Target id="@+id/rv_components" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="117" startOffset="16" endLine="126" endOffset="63"/></Target><Target id="@+id/ll_empty" view="LinearLayout"><Expressions/><location startLine="129" startOffset="16" endLine="163" endOffset="30"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="166" startOffset="16" endLine="174" endOffset="63"/></Target><Target id="@+id/fab_add" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="183" startOffset="4" endLine="190" endOffset="42"/></Target></Targets></Layout>