[{"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_component_management.xml", "source": "com.opms.app-main-73:/layout/activity_component_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_profile_compact.xml", "source": "com.opms.app-main-73:/layout/fragment_profile_compact.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_permission_edit.xml", "source": "com.opms.app-main-73:/layout/activity_permission_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_system_manage.xml", "source": "com.opms.app-main-73:/layout/fragment_system_manage.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_registration_detail.xml", "source": "com.opms.app-main-73:/layout/activity_registration_detail.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/layout_user_assignment.xml", "source": "com.opms.app-main-73:/layout/layout_user_assignment.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_profile_edit.xml", "source": "com.opms.app-main-73:/layout/activity_profile_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_profile_new.xml", "source": "com.opms.app-main-73:/layout/fragment_profile_new.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_post_edit.xml", "source": "com.opms.app-main-73:/layout/activity_post_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_business.xml", "source": "com.opms.app-main-73:/layout/fragment_business.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_user_audit.xml", "source": "com.opms.app-main-73:/layout/activity_user_audit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_customer.xml", "source": "com.opms.app-main-73:/layout/item_customer.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_user_edit.xml", "source": "com.opms.app-main-73:/layout/activity_user_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_user_audit.xml", "source": "com.opms.app-main-73:/layout/fragment_user_audit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_process_post_mapping_management.xml", "source": "com.opms.app-main-73:/layout/activity_process_post_mapping_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_password_change.xml", "source": "com.opms.app-main-73:/layout/activity_password_change.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_system_management.xml", "source": "com.opms.app-main-73:/layout/fragment_system_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_position.xml", "source": "com.opms.app-main-73:/layout/item_position.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_user_audit_list.xml", "source": "com.opms.app-main-73:/layout/activity_user_audit_list.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_profile.xml", "source": "com.opms.app-main-73:/layout/activity_profile.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_position_management.xml", "source": "com.opms.app-main-73:/layout/activity_position_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_process_template_edit.xml", "source": "com.opms.app-main-73:/layout/activity_process_template_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_post_management.xml", "source": "com.opms.app-main-73:/layout/activity_post_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_permission_management.xml", "source": "com.opms.app-main-73:/layout/activity_permission_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_product_component.xml", "source": "com.opms.app-main-73:/layout/item_product_component.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_user.xml", "source": "com.opms.app-main-73:/layout/item_user.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_process_post_mapping_edit.xml", "source": "com.opms.app-main-73:/layout/activity_process_post_mapping_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_department_tree.xml", "source": "com.opms.app-main-73:/layout/item_department_tree.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_todo.xml", "source": "com.opms.app-main-73:/layout/fragment_todo.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_position_edit.xml", "source": "com.opms.app-main-73:/layout/activity_position_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_loading.xml", "source": "com.opms.app-main-73:/layout/item_loading.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_avatar_view.xml", "source": "com.opms.app-main-73:/layout/activity_avatar_view.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_process_template.xml", "source": "com.opms.app-main-73:/layout/item_process_template.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/fragment_profile.xml", "source": "com.opms.app-main-73:/layout/fragment_profile.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_post.xml", "source": "com.opms.app-main-73:/layout/item_post.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_product_edit.xml", "source": "com.opms.app-main-73:/layout/activity_product_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_register.xml", "source": "com.opms.app-main-73:/layout/activity_register.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_login.xml", "source": "com.opms.app-main-73:/layout/activity_login.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_component_edit.xml", "source": "com.opms.app-main-73:/layout/activity_component_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_splash.xml", "source": "com.opms.app-main-73:/layout/activity_splash.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_department_management.xml", "source": "com.opms.app-main-73:/layout/activity_department_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_permission.xml", "source": "com.opms.app-main-73:/layout/item_permission.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_registration.xml", "source": "com.opms.app-main-73:/layout/item_registration.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/dialog_department_edit.xml", "source": "com.opms.app-main-73:/layout/dialog_department_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_product.xml", "source": "com.opms.app-main-73:/layout/item_product.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_user_management.xml", "source": "com.opms.app-main-73:/layout/activity_user_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_process_post_mapping.xml", "source": "com.opms.app-main-73:/layout/item_process_post_mapping.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_process_template_management.xml", "source": "com.opms.app-main-73:/layout/activity_process_template_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_business_module_checkbox.xml", "source": "com.opms.app-main-73:/layout/item_business_module_checkbox.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_product_management.xml", "source": "com.opms.app-main-73:/layout/activity_product_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_customer_edit.xml", "source": "com.opms.app-main-73:/layout/activity_customer_edit.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_pending_user.xml", "source": "com.opms.app-main-73:/layout/item_pending_user.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/item_component.xml", "source": "com.opms.app-main-73:/layout/item_component.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_main.xml", "source": "com.opms.app-main-73:/layout/activity_main.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_customer_management.xml", "source": "com.opms.app-main-73:/layout/activity_customer_management.xml"}, {"merged": "com.opms.app-mergeDebugResources-70:/layout/activity_order_management.xml", "source": "com.opms.app-main-73:/layout/activity_order_management.xml"}]