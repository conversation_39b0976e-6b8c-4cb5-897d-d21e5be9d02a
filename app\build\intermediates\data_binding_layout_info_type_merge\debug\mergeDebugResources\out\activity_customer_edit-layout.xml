<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customer_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_customer_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_customer_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="293" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="6" startOffset="4" endLine="19" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="11" startOffset="8" endLine="17" endOffset="66"/></Target><Target id="@+id/ll_image_container" view="LinearLayout"><Expressions/><location startLine="33" startOffset="12" endLine="78" endOffset="26"/></Target><Target id="@+id/card_image" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="44" startOffset="16" endLine="66" endOffset="67"/></Target><Target id="@+id/iv_customer_image" view="ImageView"><Expressions/><location startLine="51" startOffset="20" endLine="56" endOffset="72"/></Target><Target id="@+id/view_image_overlay" view="View"><Expressions/><location startLine="58" startOffset="20" endLine="64" endOffset="50"/></Target><Target id="@+id/tv_image_hint" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="76" endOffset="47"/></Target><Target id="@+id/til_customer_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="81" startOffset="12" endLine="98" endOffset="67"/></Target><Target id="@+id/et_customer_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="91" startOffset="16" endLine="96" endOffset="42"/></Target><Target id="@+id/til_customer_code" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="101" startOffset="12" endLine="118" endOffset="67"/></Target><Target id="@+id/et_customer_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="111" startOffset="16" endLine="116" endOffset="42"/></Target><Target id="@+id/til_company_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="121" startOffset="12" endLine="138" endOffset="67"/></Target><Target id="@+id/et_company_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="131" startOffset="16" endLine="136" endOffset="42"/></Target><Target id="@+id/til_address" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="141" startOffset="12" endLine="158" endOffset="67"/></Target><Target id="@+id/et_address" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="151" startOffset="16" endLine="156" endOffset="42"/></Target><Target id="@+id/til_contact" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="161" startOffset="12" endLine="179" endOffset="67"/></Target><Target id="@+id/et_contact" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="172" startOffset="16" endLine="177" endOffset="42"/></Target><Target id="@+id/til_phone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="182" startOffset="12" endLine="200" endOffset="67"/></Target><Target id="@+id/et_phone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="193" startOffset="16" endLine="198" endOffset="42"/></Target><Target id="@+id/ll_status" view="LinearLayout"><Expressions/><location startLine="203" startOffset="12" endLine="227" endOffset="26"/></Target><Target id="@+id/spinner_status" view="Spinner"><Expressions/><location startLine="221" startOffset="16" endLine="225" endOffset="55"/></Target><Target id="@+id/til_remark" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="230" startOffset="12" endLine="247" endOffset="67"/></Target><Target id="@+id/et_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="240" startOffset="16" endLine="245" endOffset="42"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="259" startOffset="16" endLine="266" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="268" startOffset="16" endLine="274" endOffset="39"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="279" startOffset="12" endLine="287" endOffset="59"/></Target></Targets></Layout>